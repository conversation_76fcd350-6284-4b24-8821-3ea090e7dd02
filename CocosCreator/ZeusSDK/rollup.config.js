import typescript from '@rollup/plugin-typescript';
import copy from 'rollup-plugin-copy';
import terser from '@rollup/plugin-terser';

const isProduction = process.env.NODE_ENV === 'production';
const useTerser = process.argv.includes('--terser');

export default {
    input: 'assets/Src/index.js',
    output: {
        file: 'ZeusSDK/index.js',
        format: 'es'
    },
    plugins: [
        typescript(),
        copy({
            targets: [
                // 复制 ThinkingData 插件
                {
                    src: 'assets/Plugins/ThinkingData/*',
                    dest: 'ZeusSDK/Plugins/ThinkingData'
                },
                // 复制 Gravity 插件
                {
                    src: 'assets/Plugins/Gravity/*',
                    dest: 'ZeusSDK/Plugins/Gravity'
                },
                // 复制 openDataContext 目录
                {
                    src: './openDataContext',
                    dest: 'ZeusSDK'
                },
                // 复制 package.json 用于模块识别
                {
                    src: 'package-dist.json',
                    dest: 'ZeusSDK',
                    rename: 'package.json'
                },
                // 复制主要类型定义文件
                {
                    src: 'index-dist.d.ts',
                    dest: 'ZeusSDK',
                    rename: 'index.d.ts'
                },
                // 复制 README 文件
                {
                    src: 'README-dist.md',
                    dest: 'ZeusSDK',
                    rename: 'README.md'
                }
            ]
        }),
        ...(useTerser ? [terser()] : [])
    ]
};
