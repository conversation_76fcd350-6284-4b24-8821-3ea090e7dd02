import {_decorator, Component} from 'cc';

const {ccclass} = _decorator;
import {ShareOptions} from "../Modules/Platform/BasePlatform";
import Logger from "../Common/Logger";
import {GraniteWrapper} from "../Granite/GraniteWrapper";
import {AppModel} from "../Helper/GlobalData";


@ccclass('ByteDanceSDK')
export class ByteDanceSDK extends Component {

    start() {

    }

    update(deltaTime: number) {

    }


    byteDanceCustomerService() {
        Logger.info("ByteDanceSDK", "byteDanceCustomerService")
        GraniteWrapper.AppPlatform?.openCustomerService((res) => {
            console.log(JSON.stringify(res, null, 2));
        });
    }

    byteDanceGetGameInfo() {
        Logger.info("ByteDanceSDK", "byteDanceGetGameInfo")
        const appModel: AppModel = {
            AppId: '7AVS2D5QH2TV',
            AppKey: 'DF864TCE1XWZE1NH',
            AppRequestURL: 'https://zeus-cn.topjoy.com',
            UserProtocolURL: '',
            PrivacyProtocolURL: '',
            DebugMode: true,
        }
        GraniteWrapper.init(appModel, (res) => {
            console.log(JSON.stringify(res, null, 2));
        });
    }

    byteDanceQuestionnaire() {
        Logger.info("ByteDanceSDK", "byteDanceQuestionnaire")
        GraniteWrapper.AppPlatform.openQuestionnaire("wxLeqv4", (res) => {
            Logger.info("ByteDanceSDK", "callback result", res)
            console.log(JSON.stringify(res, null, 2));

        });

    }

    byteDanceLogin() {
        Logger.info("ByteDanceSDK", "byteDanceLogin")
        GraniteWrapper.AppPlatform.login((res) => {
            Logger.info("ByteDanceSDK", "callback result", res)
            console.log(JSON.stringify(res, null, 2));
        })
    }

    byteDanceBilling() {
        Logger.info("ByteDanceSDK", "byteDanceBilling")
        const params = {
            productName: "",
            productId: "com.fairy.wechat.package.18",
            roleId: 'test_roleid',
            roleName: 'test_rolename',
            price: 10,
            currency: "CNY",
            billingType: "",
            billingEnv: 1,
            billingCallbackUrl: "",
            platform: "",
        }
        GraniteWrapper.AppPlatform.billing(params, (res) => {
            Logger.info("ByteDanceSDK", "callback result", res)
            console.log(JSON.stringify(res, null, 2));

        })
    }

    byteDanceCheckUpdate() {
        Logger.info("ByteDanceSDK", "byteDanceCheckUpdate")
        GraniteWrapper.AppPlatform.checkUpdate()
    }

    byteDanceGetUserInfo() {
        Logger.info("ByteDanceSDK", "byteDanceGetUserInfo")

        GraniteWrapper.AppPlatform.getUserInfo((res) => {
            Logger.info("byteDanceGetUserInfo", "callback result", res)
            console.log(JSON.stringify(res, null, 2));
        })
    }

    byteDanceShowRankList() {
        Logger.info("ByteDanceSDK", "byteDanceShowRankList")
        const data = {
            dataType: 0,
            relationType: "all",
            rankType: "day",
            suffix: "",
            rankTitle: "",
            zoneId: ""
        }
        GraniteWrapper.AppPlatform.showRankList(data)
    }

    byteDanceSetRankData() {
        Logger.info("ByteDanceSDK", "byteDanceSetRankData")
        const data = {
            dataType: 0,
            value: "120",
            priority: 0,
            zoneId: ""
        }
        GraniteWrapper.AppPlatform.setRankData(data)
    }

    byteDanceShowRewardedAd() {
        Logger.info("ByteDanceSDK", "byteDanceShowRewardedAd")
        GraniteWrapper.AppPlatform.showRewardedAd("", (res) => {
            console.log("byteDanceShowRewardedAd callback", res)
            console.log(JSON.stringify(res, null, 2));
        })
    }

    byteDanceShowInterstitialAd() {
        Logger.info("ByteDanceSDK", "byteDanceShowInterstitialAd")
        GraniteWrapper.AppPlatform.showInterstitialAd("", (res) => {
            console.log("byteDanceShowInterstitialAd callback", res)
            console.log(JSON.stringify(res, null, 2));
        })
    }

    byteDanceShare() {
        Logger.info("ByteDanceSDK", "byteDanceShare")
        const options: ShareOptions = {
            title: 'test title'
        }
        GraniteWrapper.AppPlatform.share(options, (res) => {
            console.log("share callback", res)
            console.log(JSON.stringify(res, null, 2));
        })
    }

    byteDanceNativeToScene() {
        Logger.info("ByteDanceSDK", "byteDanceNativeToScene")

        GraniteWrapper.AppPlatform.nativeToScene()
    }

    byteDanceSubscribeMessage() {
        Logger.info("ByteDanceSDK", "byteDanceSubscribeMessage")

        GraniteWrapper.AppPlatform.subscribeMessage(['MSG1835213137217498660703721556224'], (res) => {
            console.log('byteDanceSubscribeMessage callback', res)
            console.log(JSON.stringify(res, null, 2));
        })
    }

}

