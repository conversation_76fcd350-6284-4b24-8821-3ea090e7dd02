import { _decorator, Component } from 'cc';
import {GraniteWrapper} from "../Granite/GraniteWrapper";
import {AppModel} from "../Helper/GlobalData";
import Logger from "../Common/Logger";
import {NativeShareOptions, ShareOptions} from "../Modules/Platform/BasePlatform";
const { ccclass, property } = _decorator;

@ccclass('Native')
export class Native extends Component {
    start() {

    }

    update(deltaTime: number) {

    }

    init_sdk() {
        console.log('Bridge.init_sdk');
        const appModel: AppModel = {
            AppId: '7AVS2D5QH2TV',
            AppKey: 'DF864TCE1XWZE1NH',
            AppRequestURL: 'https://abroad.topjoy.com/',
            UserProtocolURL: '',
            PrivacyProtocolURL: '',
            DebugMode: true,
        }
        GraniteWrapper.init(appModel, (res) => {
            console.log(JSON.stringify(res, null, 2));
        });
    }

    login() {
        console.log('Bridge.login');
        GraniteWrapper.AppPlatform.login((res) => {
            console.log("login callback result", JSON.stringify(res, null, 2));

        })
    }

    questionaire() {
        console.log('Bridge.questionaire');
        GraniteWrapper.AppPlatform.openQuestionnaire("LBQK27G", (res) => {
            Logger.info("ByteDanceSDK", "callback result", res)
            console.log(JSON.stringify(res, null, 2));
        });
    }

    customerService() {
        console.log('Bridge.customerService');
        const kvs = {
            "level": "4"
        }
        GraniteWrapper.AppPlatform.setCustomerServiceUserInfo(kvs, (res) => {
            console.log(JSON.stringify(res, null, 2));
        });
    }

    billing() {
        console.log('Bridge.billing');
        const params = {
            productName: "",
            productId: "com.wagame.ide.6",
            roleId: 'test_roleid',
            roleName: 'test_rolename',
            price: 10,
            currency: "CNY",
            billingType: "",
            billingEnv: 1,
            billingCallbackUrl: "",
            platform: "",
            extra: "",
            roleVIP: "1",
            roleLevel: "3",
            isSubscription: "0",
            serviceId: "1",
            serviceName: "test",
        }
        GraniteWrapper.AppPlatform.billing(params, (res) => {
            Logger.info("ByteDanceSDK", "callback result", res)
            console.log(JSON.stringify(res, null, 2));

        })
    }

    getUserInfo() {
        console.log('Bridge.getUserInfo');
        GraniteWrapper.AppPlatform.getUserInfo((res) => {
            Logger.info("byteDanceGetUserInfo", "callback result", res)
            console.log(JSON.stringify(res, null, 2));
        })
    }

    showRewardedAd() {
        console.log('Bridge.showRewardedAd');
        GraniteWrapper.AppPlatform.showRewardedAd("", (res) => {
            console.log("byteDanceShowRewardedAd callback", res)
            console.log(JSON.stringify(res, null, 2));
        })
    }

    showInterstitialAd() {
        console.log('Bridge.showInterstitialAd');
        GraniteWrapper.AppPlatform.showInterstitialAd("", (res) => {
            console.log("byteDanceShowInterstitialAd callback", res)
            console.log(JSON.stringify(res, null, 2));
        })
    }

    share() {
        console.log('Bridge.share');
        const options: NativeShareOptions = {
            title: 'test title',
            text: 'test text',
            url: '',
            imageUrl: '',
            logoUrl: '',
            platform: 'facebook'
        }
        GraniteWrapper.AppPlatform.share(options, (res) => {
            console.log("share callback", res)
            console.log(JSON.stringify(res, null, 2));
        })
    }

    subscribeMessage() {
        console.log('Bridge.subscribeMessage');
        GraniteWrapper.AppPlatform.getPushToken((res) => {
            console.log('byteDanceSubscribeMessage callback', res)
            console.log(JSON.stringify(res, null, 2));
        })
    }

    getDeviceID() {
        GraniteWrapper.AppPlatform.getDeviceID((res) => {
            console.log('getDeviceID res', res)
        });
    }

    faq() {
        GraniteWrapper.AppPlatform.faq()
    }
    chat() {
        GraniteWrapper.AppPlatform.chat()
    }

}

