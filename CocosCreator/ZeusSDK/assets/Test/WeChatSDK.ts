import {_decorator, Component } from 'cc';

const {ccclass} = _decorator;
import Logger from "../Common/Logger";
import {GraniteWrapper} from "../Granite/GraniteWrapper";
import {AppModel} from "../Helper/GlobalData";

@ccclass('WeChatSDK')
export class WeChatSDK extends Component {
    start() {

    }

    update(deltaTime: number) {

    }


    wechatCustomerService() {
        Logger.info("WeChatSDK", "wechatCustomerService")
        GraniteWrapper.AppPlatform.openCustomerService((res) => {
            console.log(JSON.stringify(res, null, 2));
        });
    }

    wechatQuestionnaire() {
        Logger.info("WeChatSDK", "wechatQuestionnaire")
        GraniteWrapper.AppPlatform.openQuestionnaire("wxLeqv4", (res) => {
            Logger.info("WeChatSDK", "callback result", res)
            console.log(JSON.stringify(res, null, 2));
        });

    }

    wechatGameInfo() {
        Logger.info("WeChatSDK", "wechatGameInfo")
        const appModel: AppModel = {
            AppId: 'LGMK3H5S20RH', // fairy
            AppKey: 'KG3AJPSRJEPE568S',
            AppRequestURL: 'https://zeus-cn.topjoy.com',
            UserProtocolURL: '',
            PrivacyProtocolURL: '',
            DebugMode: true,
        }
        GraniteWrapper.init(appModel, (res) => {
            Logger.info("WeChatSDK", "callback result", res)
            console.log(JSON.stringify(res, null, 2));
        })
    }

    wechatLogin() {
        Logger.info("WeChatSDK", "wechatLogin")
        GraniteWrapper.AppPlatform.login((res) => {
            Logger.info("WeChatSDK", "callback result", res)
            console.log(JSON.stringify(res, null, 2));

        })
    }

    wechatBilling() {
        Logger.info("WeChatSDK", "wechatBilling") 
        const params = {
            productName: "",
            productId: "com.fairy.wechat.package.6",
            roleId: 'test_zeus_roleid',
            roleName: 'test_zeus_rolename',
            price: 600,
            currency: "CNY",
            billingType: "",
            billingEnv: 0,
            billingCallbackUrl: "",
            platform: "",
        }
        GraniteWrapper.AppPlatform.billing(params, (res) => {
            Logger.info("WeChatSDK", "callback result", res)
            console.log(JSON.stringify(res, null, 2));

        })
    }

    wechatCheckUpdate() {
        Logger.info("WeChatSDK", "wechatCheckUpdate")
        GraniteWrapper.AppPlatform.checkUpdate()
    }
    wechatShowRewardedAd() {
        Logger.info("WeChatSDK", "wechatShowRewardedAd")
        GraniteWrapper.AppPlatform.showRewardedAd("", (res) => {
            Logger.info("WeChatSDK", "callback result", res)
            console.log(JSON.stringify(res, null, 2));
        })
    }
    wechatShowInterstitialAd() {
        Logger.info("WeChatSDK", "wechatShowInterstitialAd")
        GraniteWrapper.AppPlatform.showInterstitialAd("", (res) => {
            Logger.info("WeChatSDK", "callback result", res)
            console.log(JSON.stringify(res, null, 2));
        })
    }

    wechatGetUserInfo() {
        Logger.info("WeChatSDK", "wechatGetUserInfo")
        GraniteWrapper.AppPlatform.getUserInfo((res) => {
            Logger.info("WeChatSDK", "callback result", res)
            console.log(JSON.stringify(res, null, 2));
        })
    }

    wechatShowGroupRankList() {
        Logger.info("WeChatSDK", "wechatShowGroupRankList")
        const data = {
            rankType: 'group',
            x: 213,
            y: 315,
            width: 200,
            height: 300
        }
        GraniteWrapper.AppPlatform.showRankList(data)
    }
    wechatShowRankList() {
        Logger.info("WeChatSDK", "wechatShowRankList")
        const data = {
            rankType: 'friend',
            x: 213,
            y: 315,
            width: 200,
            height: 300
        }
        GraniteWrapper.AppPlatform.showRankList(data)
    }

    wechatSetRankData() {
        Logger.info("WeChatSDK", "wechatSetRankData")
        const data = {
            "score":16,
            "update_time": Math.floor(Date.now() / 1000)
        }
        GraniteWrapper.AppPlatform.setRankData(data)
    }
    wechatHideOpenData() {
        Logger.info("WeChatSDK", "wechatHideOpenData")
        GraniteWrapper.AppPlatform.hideOpenData()
    }

    wechatSubscribeMessage() {
        Logger.info("WeChatSDK", "wechatSubscribeMessage")
        GraniteWrapper.AppPlatform.subscribeMessage(
            ['0livv1vdC_WTw8pTz1MRqYUdtn6cMIdenC709qsyreg', 'NQM6x8Wyx0nlFldsOmPr0J5J6BYKD6mPNAh2YTSWDCg'],
            (res) => {
                Logger.info("WeChatSDK", "callback result", res)
                console.log(JSON.stringify(res, null, 2));
            }
        )
    }

    wechatShare() {
        Logger.info("WeChatSDK", "wechatShare")
        GraniteWrapper.AppPlatform.share({ title: '测试小游戏标题'}, (res) => {
            Logger.info("WeChatSDK", "callback result", res)
            console.log(JSON.stringify(res, null, 2));
        })
    }
}

