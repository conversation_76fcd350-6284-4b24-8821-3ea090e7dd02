
export default class AccountConstants {
    public static readonly LOCAL_ACCOUNT_KEY: string = "zeus_account";
    public static readonly LOCAL_ID_KEY: string = "zeus_userId";
}

export class BIEvent {
    public static readonly REGISTER_EVENT: string = "RoleRegister";
    public static readonly LOGIN_EVENT: string = "RoleLogin";
    public static readonly BATTLE_EVENT: string = "BattleFlow";
    public static readonly AD_EVENT: string = "AdPointFlow";
    public static readonly GUIDE_EVENT: string = "ClientGuideStep";
}

export class AdFlow {
    public static readonly AD_CLICKED: number = 1;
    public static readonly AD_SHOWED: number = 2;
    public static readonly AD_COMPLETE: number = 3;
}


export enum LoginType {
    Wechat = 9,
    DouYin = 13
}