// Logger.ts
type LogLevel = 'debug' | 'info' | 'warn' | 'error';

class Logger {
    private static currentLevel: LogLevel = 'info'; // 默认日志级别

    // 设置当前日志级别
    public static setLogLevel(level: LogLevel): void {
        this.currentLevel = level;
    }

    // 调试日志（DEBUG）
    public static debug(...args: any[]): void {
        if (this.currentLevel === 'debug') {
            console.debug('[DEBUG]', ...args);
        }
    }

    // 信息日志（INFO）
    public static info(...args: any[]): void {
        if (['debug', 'info'].indexOf(this.currentLevel) !== -1) {
            console.info('[INFO]', ...args);
        }
    }

    // 警告日志（WARN）
    public static warn(...args: any[]): void {
        if (['debug', 'info', 'warn'].indexOf(this.currentLevel) !== -1) {
            console.warn('[WARN]', ...args);
        }
    }

    // 错误日志（ERROR）
    public static error(...args: any[]): void {
        console.error('[ERROR]', ...args);
    }
}

export default Logger;
