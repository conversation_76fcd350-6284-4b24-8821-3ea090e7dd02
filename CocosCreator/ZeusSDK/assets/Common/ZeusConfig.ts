export enum Module {
    THINKING = "thinking",
    ADJUST = "adjust",
    GRAVITY = "gravity",
    WECHAT = "wechat",
    BYTE_DANCE = "byteDance"
}

export enum AdMedia {
    WECHAT = "wechat", // 广点通
    BYTE_DANCE = "byteDance", // 巨量引擎
}

export enum AdType {
    REWARD = "reward",
    INTERSTITIAL = "interstitial",
    REWARD_INTERSTITIAL = "reward_interstitial"
}

interface ThinkingData {
    server_url: string
    app_id: string
    is_error_tracker?: boolean
}

interface Gravity {
    access_token: string, // 项目通行证，在：网站后台-->设置-->应用列表中找到Access Token列 复制（首次使用可能需要先新增应用）
    name: "ge", // 全局变量名称
    debug: boolean,
}

interface Adjust {
    app_token: string,
    is_debug: boolean
}

interface WeChat {
    app_id: string
    billing_env?: number
    billing_qrcode_image_url?: string
}

interface ByteDance {
    app_id: string,
    billing_env: number,
    ad?: {}
}

export interface Config {
    thinking: ThinkingData[],
    gravity: Gravity,
    wechat?: WeChat,
    byteDance?: ByteDance,
    adjust: Adjust
}

export const ZeusConfig: { [index: string]: Config } = {}


export function getConfig(): Config {
    return ZeusConfig["default"]
}

export function getModuleConfigValue(module_name: string, module_sub_key: string) {
    let conf: Config = getConfig()
    return conf[module_name][module_sub_key]
}