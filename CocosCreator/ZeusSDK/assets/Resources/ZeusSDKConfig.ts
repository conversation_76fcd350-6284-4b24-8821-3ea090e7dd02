import {ZeusConfig} from "../Common/ZeusConfig"


ZeusConfig["default"] = {
    thinking: [
        {
            server_url: "https://log-data.topjoy.com/",
            app_id: "ss",
        },
        {
            server_url: "https://log-data.topjoy.com/",
            app_id: "ss",
            is_error_tracker: true
        },
    ],
    wechat: {
        app_id: "wx05dd926e19017db6",
        billing_env: 0,  // 0 米大师正式环境 // 1 米大师沙箱环境
        billing_qrcode_image_url: 'https://pandora-packages.oss-cn-beijing.aliyuncs.com/client/picture/clickpay.wad_0.20240304184623.png',
    },
    gravity: {
        access_token: "g8jX3lmadU2YHafqsqKruevunttyycop",
        name: "ge",
        debug: false
    },
    byteDance: {
        app_id: "tt0eeb785691dcd5dc02",
        billing_env: 0,
    },
    adjust: {
        app_token: "2fm1gkqubvpc",
        is_debug: true,
    },
}