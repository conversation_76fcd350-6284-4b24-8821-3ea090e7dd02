import {getConfig} from "../Common/ZeusConfig";
import {Device} from "../Helper/DeviceDetails";
import {Md5} from "../Helper/HashLib";

export class Analytics {
    // https://docs.thinkingdata.jp/ta-manual/v4.3/installation/installation_menu/client_sdk/game_engine_sdk_installation/cocoscreator_sdk_installation/cocoscreator_sdk_installation.html
    // 版本V3.2.0 https://docs-v2.thinkingdata.jp/?version=latest&code=cocoscreator_sdk_installation&anchorId=&lan=zh-CN

    private static instance: Analytics | null = null;
    private _errorTrackerAppId: string;
    private _sessionId: string;
    private _config = [];

    private get config() {
        const sdkConfig = getConfig();
        for (const item of sdkConfig.thinking) {
            this._config.push({
                appId: item.app_id,
                serverUrl: item.server_url,
                isErrorTracker: item.is_error_tracker ?? false,
                autoTrack: {
                    appShow: true, // 自动采集 ta_mg_show
                    appHide: true // 自动采集 ta_mg_hide
                }
            })
        }

        return this._config;
    }

    private constructor() {
        try {
            this.config.forEach((item) => {
                if (item.isErrorTracker) {
                    this._errorTrackerAppId = item.appId;
                }
                TDAnalytics.init(item);
            })
        } catch(err) {
            throw new Error(`ThinkingDataAgent Init Error: ${err.message}.` + "且数数app_id, 和server_url为重要配置, 否则数据上报功能不能正常工作。 " +
                "请参考文档: https://topjoy.yuque.com/tsd/zzske8/qn3dgoxtdp0f3x8a")
        }

        this.setSuperProperties({
            "device_id": Device.deviceId,
            "session_id": this.SessionId,
        });
    }

    get SessionId() {
        if (!this._sessionId) {
            this._sessionId = Md5.hashStr(Date.now().toString()).toString();
        }

        return this._sessionId;
    }

    public static getInstance(): Analytics {
        if (!this.instance) {
            this.instance = new Analytics();
        }
        return this.instance;
    }


    public setSuperProperties(properties: any) {
        this.config.forEach((item) => {
            TDAnalytics.setSuperProperties(properties, item.appId);
        })
    }

    public userSet(properties: any) {
        this.config.forEach((item) => {
            TDAnalytics.userSet(properties, item.appId);
        })
    }

    public userSetOnce(properties: any) {
        this.config.forEach((item) => {
            TDAnalytics.userSetOnce(properties, item.appId);
        })
    }

    public login(accountId: string) {
        this.config.forEach((item) => {
            TDAnalytics.login(accountId, item.appId);
        })
    }

    public getDistinctId() {
        return TDAnalytics.getDistinctId();
    }

    public trackEvent(eventName: string, properties: any) {
        const normalInstances = this.config.filter(
            (item) => !item.isErrorTracker
        );
        for (const instance of normalInstances) {
            TDAnalytics.track({"eventName": eventName, "properties": properties}, instance.appId)
        }
    }

    public trackRevenue(eventName: string, price: number, currency: string, properties: any) {
        const normalInstances = this.config.filter(
            (item) => !item.isErrorTracker
        );
        for (const instance of normalInstances) {
            properties.set("price", price);
            properties.set("currency", currency);
            TDAnalytics.track({"eventName": eventName, "properties": properties}, instance.appId)
        }
    }

    public trackError(eventName: string, properties: any) {
        TDAnalytics.track({"eventName": eventName, "properties": properties}, this._errorTrackerAppId);
    }
}