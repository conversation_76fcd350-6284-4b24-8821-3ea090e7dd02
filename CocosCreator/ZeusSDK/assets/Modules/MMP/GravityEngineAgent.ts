import {Analytics} from "../Analytics";
import {getModuleConfigValue, Module} from "../../Common/ZeusConfig";
import Logger from "../../Common/Logger";

export class GravityEngineAgent {
    // https://doc.gravity-engine.com/turbo-integrated/mini-program/wechat/mp-sdk.html#_2-2-%E5%85%AC%E5%85%B1%E5%B1%9E%E6%80%A7

    private static instance: GravityEngineAgent | null = null;
    private ge: GravityAnalyticsAPI;

    public static getInstance() {
        if (!this.instance) {
            this.instance = new GravityEngineAgent();
        }
        return this.instance;
    }

    private constructor() {
        const accessToken = getModuleConfigValue(Module.GRAVITY, "access_token");
        const name = getModuleConfigValue(Module.GRAVITY, "name");
        let distinctId = '';
        if (accessToken == '' || name == '') {
            throw new TypeError("GravityEngine Init Error, 巨量access_token和name为重要配置, 请确认是否正确配置. 请参考文档: https://topjoy.yuque.com/tsd/zzske8/qn3dgoxtdp0f3x8a")
        }
        try {
            distinctId = Analytics.getInstance().getDistinctId();
        } catch (err) {
            throw new Error(`Error: ${err?.message}` + "ThinkingData GetDistinctId failed. This might indicate ThinkingData initialization failure. Please ensure ThinkingData is properly initialized before starting GravityEngine.")
        }
        const config = {
            clientId: distinctId, // 用户唯一标识，如产品为小游戏，则必须填用户openid（注意，不是小游戏的APPID！！！）
            accessToken: accessToken, // 项目通行证，在：网站后台-->设置-->应用列表中找到Access Token列 复制（首次使用可能需要先新增应用）
            name: name, // 全局变量名称
            debugMode: getModuleConfigValue(Module.GRAVITY, "debug") == true ? "debug" : null, // 是否开启测试模式，开启测试模式后，可以在 网站后台--设置--元数据--事件流中查看实时数据上报结果。（测试时使用，上线之后一定要关掉，改成none或者删除）
            autoTrack: {
                appLaunch: true, // 自动采集 $MPLaunch
                appShow: true, // 自动采集 $MPShow
                appHide: true, // 自动采集 $MPHide
            },
        };

        this.ge = new GravityAnalyticsAPI(config);
        this.ge.setupAndStart();
    }

    public setIdentifier(openid: string, extraParams: any) {
        /**
         * 在微信登录成功后，第一时间调用该方法，设置openid
         * @param {string} name
         * @param {number} version   用户初始化的程序发布更新的版本号（必填）
         * @param {string} openid    open id (小程序/小游戏必填)
         */

        let version: number;
        if (extraParams === null) {
            version = 0;
        } else {
            version = extraParams["version"] ?? 0;
        }

        this.ge.initialize({
            name: openid, // 用户名，可以理解成用户在业务中的昵称，如果没有，可以填用户唯一ID（必填）。 静默登录过程中不会要求用户授权昵称。
            version: version,
            openid: openid,
            enable_sync_attribution: false,
        }).then((res) => {
            Logger.info("initialize success " + res);
        }).catch((err) => {
            Logger.info("initialize failed, error is " + err);
        });

        const ta = Analytics.getInstance();
        this.ge.bindTAThirdPlatform(openid, ta.getDistinctId());
    }

    public setSuperProperties(properties: any) {
        this.ge.setSuperProperties(properties);
    }

    public trackRegister() {
        this.ge.registerEvent();
    }

    public trackEvent(eventName: string, properties: any) {
        this.ge.track(eventName, properties);
    }

    public trackRevenue(amount: number, currency: string, orderId: string, properties: any) {
        /**
         * 上报付费事件
         * @param amount     付费金额 单位为分
         * @param currency   货币类型 按照国际标准组织ISO 4217中规范的3位字母，例如CNY人民币、USD美金等
         * @param orderId    订单号 引力引擎会通过订单号 orderId 去重，避免重复上报，请务必传入！
         * @param properties 额外字段 支持的值有 payReason (支付原因), payMethod (支付方式). 可不传.
         */
        let payReason: string = properties["payReason"] ?? "";
        let payMethod: string = properties["payMethod"] ?? "";

        this.ge.payEvent(amount, currency, orderId, payReason, payMethod);
    }

    public trackShowAd(adType: string, adUnitId: string, properties: any) {
        /**
         * 上报广告展示事件 参数如下
         * @param adType           广告类型 （取值为：reward、banner、native、interstitial、video_feed、video_begin，分别对应激励视频广告、Banner广告、原生模板广告、插屏广告、视频广告、视频贴片广告）
         * @param adUnitId         广告位ID（一般以adunit开头，注意不要填错，会导致广告收入统计不准！）
         * @param properties       其他需要携带的自定义参数
         */
        this.ge.adShowEvent(adType, adUnitId, properties);
    }
}