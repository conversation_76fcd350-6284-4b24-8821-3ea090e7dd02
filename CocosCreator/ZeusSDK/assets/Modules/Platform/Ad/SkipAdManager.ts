import AdManager from "./AdManager";
import {Action} from "../../../Core/Runtime/Action";
import Ad, {AdCode} from "../../../Core/Runtime/Result/Advertiser";
import {AdMedia, AdType} from "../../../Common/ZeusConfig";

export class SkipAdManager extends AdManager {
    constructor(media: any, media_type: AdMedia) {
        super(media, media_type);
    }

    public showRewardedAd(placement: string, callback: Action<Ad>) {
        this._type = AdType.REWARD;
        this._callback = callback;
        this.complete('', AdCode.Success.jsonWithMsg(""))
    }
    public showInterstitialAd(placement: string, callback: Action<Ad>) {
        this._type = AdType.INTERSTITIAL;
        this._callback = callback;
        this.complete('', AdCode.Success.jsonWithMsg(""))
    }

    protected complete(adUnitId: string, resp: any) {
        resp['uuid'] = Date.now().toString();
        resp['type'] = this._type;
        resp['platform'] = this.mediaType;
        let adResult: Ad = new Ad();
        adResult.parse(resp);
        adResult.ADResult.adUnitId = '';
        this._callback(adResult);
    }

}