import Logger from "../../../Common/Logger";
import {Action} from "../../../Core/Runtime/Action";
import Ad, {AdCode} from "../../../Core/Runtime/Result/Advertiser";
import {AdMedia, AdType} from "../../../Common/ZeusConfig";
import {GravityEngineAgent} from "../../MMP/GravityEngineAgent";
import {Analytics} from "../../Analytics";
import {AdFlow, BIEvent} from "../../../Common/Constants";
import {GlobalContainer} from "../../../Helper/GlobalData";
import UUID from "../../../Helper/UUID";

const globalContainer = GlobalContainer.getInstance();

export default class AdManager {
    // https://developers.weixin.qq.com/minigame/dev/guide/open-ability/ad/rewarded-video-ad.html
    private readonly media: any;
    protected readonly mediaType: string;
    private readonly rewardAdUnitId: string;
    private readonly interstitialAdUnitId: string;
    private rewardedVideoAd: any | null = null;
    private interstitialAd: any | null = null;
    protected _type: string = null;
    private _uuid: string = null;
    protected _callback: Action<Ad> = null;

    constructor(media: any, media_type: AdMedia) {
        this.media = media;
        this.mediaType = media_type;
        if (media_type === AdMedia.WECHAT) {
            this.rewardAdUnitId = globalContainer.getWechatADConfig()?.rewardAdUnitId;
            this.interstitialAdUnitId = globalContainer.getWechatADConfig()?.interstitialAdUnitId;
        } else if (media_type === AdMedia.BYTE_DANCE) {
            this.rewardAdUnitId = globalContainer.getDouYinADConfig()?.rewardAdUnitId;
            this.interstitialAdUnitId = globalContainer.getDouYinADConfig()?.interstitialAdUnitId;
        } else {
            Logger.error(`unknown media type: ${media_type}`);
        }
        if (!this.rewardAdUnitId || !this.interstitialAdUnitId) {
            Logger.error(`could not find ${this.mediaType} ad unit id set`)
        }
        this.init();
    }

    // 初始化广告实例
    private init() {
        this.initRewardedVideoAd();
        this.initInterstitialAd();
    }

    private initRewardedVideoAd() {
        console.log("initRewardedVideoAd 激励广告初始化")
        if (!this.media.createRewardedVideoAd) {
            Logger.error('当前基础库不支持激励视频广告');
            return;
        }

        this.rewardedVideoAd = this.media.createRewardedVideoAd({
            adUnitId: this.rewardAdUnitId
        });

        this.rewardedVideoAd.onLoad(() => {
            Logger.debug("rewarded ad loaded successfully")
        });

        this.rewardedVideoAd.onError((err) => {
            Logger.debug(`rewarded ad loaded failed: ${err}`)
        });
    }

    private initInterstitialAd() {
        console.log("initInterstitialAd 插屏广告初始化")
        this.interstitialAd = this.media.createInterstitialAd({
            adUnitId: this.interstitialAdUnitId
        });

        this.interstitialAd.onLoad(() => {
            Logger.debug("interstitial ad loaded successfully")
        });

        this.interstitialAd.onError((err) => {
            Logger.debug(`interstitial ad loaded failed: ${err}`)
        });
    }

    public showRewardedAd(placement: string, callback: Action<Ad>) {
        this._uuid = UUID.generate();

        const properties = {
            ad_placement: placement,
            ad_type: AdType.REWARD,
            ad_media: this.mediaType,
            ad_step: AdFlow.AD_CLICKED,
            ad_unit_id: this.rewardAdUnitId,
            ad_session_id: this._uuid,
        }
        Analytics.getInstance().trackEvent(BIEvent.AD_EVENT, properties)

        if (this.rewardedVideoAd == null) {
            Logger.warn("rewardedAd is not ready")
            return
        }

        // 保存回调和相关信息
        this._type = AdType.REWARD;
        this._callback = callback;

        // 添加回调处理标志，防止多次回调
        let callbackHandled = false;

        // 统一的成功回调处理
        const handleSuccess = () => {
            if (callbackHandled) return;
            callbackHandled = true;

            properties.ad_step = AdFlow.AD_COMPLETE
            Analytics.getInstance().trackEvent(BIEvent.AD_EVENT, properties)

            // 正常播放结束，可以下发游戏奖励
            this.complete(this.rewardAdUnitId, AdCode.Success.jsonWithMsg(""));
            this.complete(this.rewardAdUnitId, AdCode.ADHidden.jsonWithMsg(""));
            this.complete(this.rewardAdUnitId, AdCode.ADUserRewarded.jsonWithMsg(""));
        };

        // 统一的取消回调处理
        const handleCancel = () => {
            if (callbackHandled) return;
            callbackHandled = true;

            this.cancel(this.rewardAdUnitId);
        };

        // 先移除之前的事件监听器，避免重复绑定
        this.rewardedVideoAd.offClose();
        this.rewardedVideoAd.offError();

        // 监听广告关闭事件
        this.rewardedVideoAd.onClose(res => {
            Logger.debug(`RewardedClose 广告关闭 ${res.isEnded} | ${res.errMsg}`);
            GravityEngineAgent.getInstance().trackShowAd(AdType.REWARD, this.rewardAdUnitId, null)
            if (res && res.isEnded) {
                handleSuccess();
            } else {
                handleCancel();
            }
        });

        // 监听广告错误事件
        this.rewardedVideoAd.onError((err) => {
            Logger.debug(`RewardedError 广告错误 ${err.errCode} | ${err.errMsg}`);
            if (callbackHandled) return;
            callbackHandled = true;

            this.complete(this.rewardAdUnitId, AdCode.ADDisplayFailed.jsonWithMsg(err.errMsg));
        });

        this.showRewardedVideoAdWithRetry(properties, callbackHandled);
    }

    private showRewardedVideoAdWithRetry(properties: any, callbackHandled: boolean): Promise<void> {
        return this.rewardedVideoAd.show()
            .then(() => {
                console.log('激励视频 广告显示');
                Logger.debug("rewardedVideoAd.show========");
                properties.ad_step = AdFlow.AD_SHOWED;
                if (!callbackHandled) {
                    this.complete(this.rewardAdUnitId, AdCode.ADDisplayed.jsonWithMsg(""));
                }
                Analytics.getInstance().trackEvent(BIEvent.AD_EVENT, properties);
            })
            .catch(() => {
                // 失败重试，主动load一下广告
                return this.rewardedVideoAd.load()
                    .then(() => {
                        return this.rewardedVideoAd.show();
                    })
                    .then(() => {
                        console.log('激励视频 广告显示（重试成功）');
                        Logger.debug("rewardedVideoAd.show retry complete");
                        properties.ad_step = AdFlow.AD_SHOWED;
                        if (!callbackHandled) {
                            this.complete(this.rewardAdUnitId, AdCode.ADDisplayed.jsonWithMsg(""));
                        }
                        Analytics.getInstance().trackEvent(BIEvent.AD_EVENT, properties);
                    })
                    .catch((err) => {
                        if (callbackHandled) return;
                        callbackHandled = true;

                        let errMsg = '';
                        if (typeof err === 'string') {
                            errMsg = err;
                        } else if (err && typeof err.errMsg === 'string') {
                            errMsg = err.errMsg;
                        } else if (err && err.toString) {
                            errMsg = err.toString();
                        }
                        this.complete(this.rewardAdUnitId, AdCode.ADIsNotReady.jsonWithMsg(errMsg));
                    });
            });
    }

    public showInterstitialAd(placement: string, callback: Action<Ad>) {
        this._uuid = UUID.generate();
        const properties = {
            ad_placement: placement,
            ad_type: AdType.INTERSTITIAL,
            ad_media: this.mediaType,
            ad_step: AdFlow.AD_CLICKED,
            ad_unit_id: this.interstitialAdUnitId,
            ad_session_id: this._uuid,
        }
        Analytics.getInstance().trackEvent(BIEvent.AD_EVENT, properties)

        if (this.interstitialAd == null) {
            this.initInterstitialAd();
        }

        // 保存回调和相关信息
        this._type = AdType.INTERSTITIAL;
        this._callback = callback;

        // 添加错误处理标志
        let errorHandled = false;

        // 统一的错误处理函数
        const handleError = (err: any) => {
            if (errorHandled) return; // 如果已经处理过错误，直接返回
            errorHandled = true;

            // 安全地获取错误消息
            let errMsg = '';
            if (typeof err === 'string') {
                errMsg = err;
            } else if (err && typeof err.errMsg === 'string') {
                errMsg = err.errMsg;
            } else if (err && err.toString) {
                errMsg = err.toString();
            }
            console.log(`InterstitialError 广告错误 ${err.errCode || ''} | ${errMsg}`);
            this.complete(this.interstitialAdUnitId, AdCode.ADDisplayFailed.jsonWithMsg(errMsg));
        };

        // 先移除之前的事件监听器，避免重复绑定
        this.interstitialAd.offClose();
        this.interstitialAd.offError();

        // 监听广告关闭事件
        this.interstitialAd.onClose(res => {
            Logger.debug("InterstitialClose 广告关闭");
            console.log("InterstitialClose 广告关闭");
            GravityEngineAgent.getInstance().trackShowAd(AdType.INTERSTITIAL, this.interstitialAdUnitId, null);

            properties.ad_step = AdFlow.AD_COMPLETE;
            Analytics.getInstance().trackEvent(BIEvent.AD_EVENT, properties);

            // 正常播放结束，可以下发游戏奖励
            this.complete(this.interstitialAdUnitId, AdCode.ADDisplayed.jsonWithMsg(""));
            this.complete(this.interstitialAdUnitId, AdCode.Success.jsonWithMsg(""));
            this.complete(this.interstitialAdUnitId, AdCode.ADHidden.jsonWithMsg(""));
            this.complete(this.interstitialAdUnitId, AdCode.ADUserRewarded.jsonWithMsg(""));
            // 只在抖音平台执行销毁操作，微信不需要销毁
            if (this.mediaType === AdMedia.BYTE_DANCE) {
                this.interstitialAd.destroy();
                this.interstitialAd = null;
            }

        });

        // 监听广告错误事件
        this.interstitialAd.onError(handleError);

        this.interstitialAd.show()
            .then(() => {
                console.log('插屏广告显示');
                properties.ad_step = AdFlow.AD_SHOWED;
                Analytics.getInstance().trackEvent(BIEvent.AD_EVENT, properties);
            })
            .catch(handleError); // 使用相同的错误处理函数
    }

    // 回调
    protected complete(adUnitId: string, resp: any) {
        resp.uuid = this._uuid;
        resp.type = this._type;
        resp.platform = this.mediaType;
        let adResult: Ad = new Ad();
        adResult.parse(resp);
        adResult.ADResult.adUnitId = adUnitId;
        this._callback(adResult);
    }

    // 取消回调
    private cancel(adUnitId: string) {
        let json = AdCode.Cancel.jsonWithMsg(AdCode.Cancel.Message);
        json["uuid"] = this._uuid;
        json["type"] = this._type;
        json["platform"] = this.mediaType;
        let adResult: Ad = new Ad();
        adResult.parse(json);
        adResult.ADResult.adUnitId = adUnitId;
        this._callback(adResult);
    }
}
