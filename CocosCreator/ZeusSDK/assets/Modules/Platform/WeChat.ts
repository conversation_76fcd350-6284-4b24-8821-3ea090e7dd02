import ZeusServer from "../../Helper/ZeusServer";
import {<PERSON>ingParams, LoginParams} from "../../Modules/Platform/Typing";
import {AdMedia, Module} from "../../Common/ZeusConfig";
import Login, {LoginCode} from "../../Core/Runtime/Result/Login";
import {Action} from "../../Core/Runtime/Action";
import AccountConstants, {BIEvent, LoginType} from "../../Common/Constants";
import Base, {BaseCode} from "../../Core/Runtime/Result/Base";
import {BillingCode} from "../../Core/Runtime/Result/Billing";
import Ad from "../../Core/Runtime/Result/Advertiser";
import Notification from "../../Core/Runtime/Result/Notification";
import Network from "../../Core/Runtime/Result/Network";
import Questionnaire from "../../Core/Runtime/Result/Questionnaire";
import Logger from "../../Common/Logger";
import AdManager from "./Ad/AdManager";
import {Analytics} from "../../Modules/Analytics";
import {ZeusSDKVersion} from "../../Helper/ZeusSDKVersion";
import { GetUserInfo } from "../../Core/Runtime/Result/GetUserInfo";
import {LocalData} from "../../Helper/LocalData";
import {GravityEngineAgent} from "../../Modules/MMP/GravityEngineAgent";
import {GlobalContainer} from "../../Helper/GlobalData"
import {ZeusTracker} from "../../Core/Runtime/Event/zeusTracker";
import {IWeChatAdapter} from "../../Interfaces/BaseInterface";
import {InstantBaseAdapter, ShareOptions} from "../../Modules/Platform/BasePlatform";
import {InitResult} from "../../Core/Runtime/Result/init";
import {getAdManager} from "./Ad/AdManagerFactory";

enum BillingType {
    IOS = "jsapi",
    ANDROID = "virtual-payment",
    UNKNOWN = "unknown"
}

const globalContainer = GlobalContainer.getInstance();

class RankOption {
    type: string;
    event: string;
    rankType: string;
    shareTicket: string;
    x: number;
    y: number;
    width: number;
    height: number;
    constructor(dict) {
        this.type = 'engine';
        this.event = 'viewport';
        this.rankType = dict.rankType;
        this.shareTicket = dict.shareTicket || '';
        this.x = dict.x || 0;
        this.y = dict.y || 0;
        this.width = dict.width || 0;
        this.height = dict.height || 0;
    }
}

export class WeChatAdapter extends InstantBaseAdapter{
    private readonly wx: any;
    private readonly loginType = "9"
    private _deviceInfo: any
    private _adManager: AdManager;
    private _analytics : Analytics | null = null;
    private static instance: WeChatAdapter | null = null;
    private readonly source_name: string = "wechat"
    private _rankListOpenDataContext: any
    private _shareStartTime: any
    private hasAddedOnShowListener: boolean
    private _isLogin: boolean;
    private _shareTicket: string;
    private _isInit: boolean;

    private constructor() {
        super();
        this.wx = globalThis.wx;
        this._analytics = Analytics.getInstance();
        this._analytics.setSuperProperties({"source_name": this.source_name})
    }

    private get deviceInfo() {
        if (!this._deviceInfo) {
            this._deviceInfo = this.wx.getDeviceInfo();
        }

        return this._deviceInfo;
    }

    get platform() {
        return this.deviceInfo.platform
    }

    public static getInstance() {
        if (!this.instance) {
            this.instance = new WeChatAdapter();
        }
        return this.instance;
    }

    public init(callback: Action<InitResult>) {
        ZeusServer.gameInfo(this.wx,
            (res) => {
                Logger.debug("get game info successfully: ", res)
                const respResult = res.data?.result;
                if (respResult) {
                    if (respResult['wechatAdConfig']) {
                        globalContainer.setWechatADConfig({
                            rewardAdUnitId: respResult['wechatAdConfig']['rewarded_ad_id'],
                            interstitialAdUnitId: respResult['wechatAdConfig']['interstitial_ad_id']
                        })
                    }
                }
                respResult['code'] = BaseCode.Success.Code;
                this._isInit = true;
                this._adManager = getAdManager(false, this.wx, AdMedia.WECHAT);
                const tracker = ZeusTracker.getInstance();
                tracker.init();
                callback(respResult);
            },
            (err) => {
                Logger.debug("get game info failed: ", err)
                let baserResult = new InitResult();
                const jsonData = BaseCode.ZeusServiceFail.jsonWithMsg("");
                baserResult.parse(jsonData);
                callback(baserResult);
            }
        )
    }

    public login(callback: Action<InitResult | Login>) {
        const self = this;
        if (!self._isInit) {
            let result = new InitResult();
            result.parse(BaseCode.SDKNotInit.jsonWithMsg(''));
            callback(result);
            return;
        }
        Logger.debug("check wechat session status")
        this.wx.checkSession({
            success: function (res: any) {
                let account = LocalData.get(AccountConstants.LOCAL_ACCOUNT_KEY)
                Logger.debug("trying to get account from local storage: ", account, "result: ", res)

                if (account) {
                    Logger.debug("found account in local storage: ", account, " start verify login status with Zeus Server");
                    let loginResult = new Login();
                    ZeusServer.verifyLoginStatus(self.wx, account,
                        function (res) {
                            if (res && res?.data?.result) {
                                res.data.result["login_type"] = LoginType.Wechat;
                                Logger.debug("verify login status successfully: ", res);
                                loginResult.parse(ZeusServer.normalizeZeusServerResponse(res));
                                self.loginSuccess(loginResult);

                                self._analytics.setSuperProperties({
                                    "open_id": loginResult.Detail.openid,
                                    "zeus_open_id": loginResult.Account,
                                    "zeus_login_way": loginResult.LoginType,
                                    "zeus_version": ZeusSDKVersion.SDKVersion,
                                })
                                self._analytics.login(account);
                                self._analytics.trackEvent(BIEvent.LOGIN_EVENT, {})

                                // 在静默登录场景下不会要求用户授权获取用户名称，这里用open id代替。
                                const props = {"user_name": loginResult.Detail.openid};
                                GravityEngineAgent.getInstance().setIdentifier(loginResult.Detail.openid, props)

                                callback(loginResult);
                            } else {
                                Logger.debug("verify login status failed: ", res);
                                self._isLogin = false;
                                loginResult.parse(ZeusServer.normalizeZeusServerResponse(res));
                                callback(loginResult);
                            }
                        },
                        function (res) {
                            Logger.debug("verify login status failed: ", res);
                            self._isLogin = false;
                            loginResult.parse(ZeusServer.normalizeZeusServerResponse(res));
                            callback(loginResult);
                        });
                } else {
                    Logger.debug("no account in local storage: ", account, " start login with wechat");
                    self.doLogin(callback)
                }
            },
            fail: function (res: any) {
                Logger.debug("wechat session check failed: ", res, " start login with wechat");
                self._isLogin = false;
                self.doLogin(callback);
            }
        })
    }

    private doLogin(callback: Action<Login>) {
        const self = this;
        let loginResult = new Login();
        this.wx.login({
            success: function (res: any) {
                const loginParams: LoginParams = {
                    loginType: self.loginType,
                    code: res.code,
                    platform: self.platform,
                    mobileInfo: self.deviceInfo
                }
                Logger.debug("wechat login successfully: ", res, " start login with Zeus Server");
                ZeusServer.login(self.wx, loginParams,
                    function (res) {
                        Logger.debug("Zeus Server login successfully: ", res)
                        res.data.result["login_type"] = LoginType.Wechat;
                        loginResult.parse(ZeusServer.normalizeZeusServerResponse(res));
                        self.loginSuccess(loginResult);

                        self._analytics.login(loginResult.Account);
                        self._analytics.setSuperProperties({
                            "open_id": loginResult.Detail.openid,
                            "zeus_open_id": loginResult.Account,
                            "zeus_login_way": loginResult.LoginType,
                            "zeus_version": ZeusSDKVersion.SDKVersion,
                        })
                        self._analytics.trackEvent(BIEvent.LOGIN_EVENT, {});

                        // 在静默登录场景下不会要求用户授权获取用户名称，这里用open id代替。
                        const props = {"user_name": loginResult.Detail.openid};
                        GravityEngineAgent.getInstance().setIdentifier(loginResult.Detail.openid, props);
                        if (loginResult.IsFirstRegister) {
                            GravityEngineAgent.getInstance().trackRegister();
                        }

                        callback(loginResult);
                    },
                    function (res) {
                        Logger.debug("Zeus Server login failed: ", res)
                        self._isLogin = false;
                        loginResult.parse(ZeusServer.normalizeZeusServerResponse(res));
                        callback(loginResult);
                    });
            },
            fail: function (err: any) {
                Logger.debug("wechat login failed: ", err)
                self._isLogin = false;
                const res = {
                    code: LoginCode.ThirdSDKReturnErrorCode.Code,
                    msg: `with code: ${err.errno} error: ${err.errMsg}`
                }
                loginResult.parse(res);
                callback(loginResult);
            },
            complete: function (res: any) {
                Logger.info('login complete', res);
            },
        })
    }

    private loginSuccess(loginResult: Login) {
        this._isLogin = true;
        const isSkipAd = loginResult.IsSkipAd;
        this._adManager = getAdManager(isSkipAd, this.wx, AdMedia.WECHAT)
        LocalData.save(AccountConstants.LOCAL_ACCOUNT_KEY, loginResult.Account);
        LocalData.save(AccountConstants.LOCAL_ID_KEY, loginResult.ID.toString());
    }


    private doGetUserInfo(callback: Action<GetUserInfo>) {
        this.wx.getUserInfo({
            success: (res) => {
                Logger.debug('getUserInfo success', res);
                let userInfoResult = new GetUserInfo();
                const result = {
                    code: BaseCode.Success.Code,
                    msg: res.errMsg,
                    errMsg: res.errMsg,
                    userInfo: res.userInfo
                };
                userInfoResult.parse(result);
                callback(userInfoResult);
            },
            fail: (err) => {
                Logger.debug('getUserInfo failed', err);
                let userInfoResult = new GetUserInfo();
                const result = {
                    code: BaseCode.ThirdSDKReturnErrorCode.Code,
                    msg: err.errMsg,
                    errMsg: err.errMsg
                };
                userInfoResult.parse(result);
                callback(userInfoResult);
            }
        });
    }

    public getUserInfo(callback: Action<Base>) {
        if (this._isLogin) {
            const systemInfo = this.wx.getSystemInfoSync();
            const screenWidth = systemInfo.screenWidth;
            const screenHeight = systemInfo.screenHeight;
            const buttonStyle = {
                type: 'text',
                text: '',
                style: {
                    left: 0,
                    top: 0,
                    width: screenWidth,
                    height: screenHeight,
                    backgroundColor: 'transparent', // 设置背景透明
                    color: 'transparent', // 设置文本颜色透明（如果有文本）
                    borderColor: 'transparent' // 设置边框颜色透明
                }
            }
            this.wx.getSetting({
                success: (res) => {
                    if (res.authSetting['scope.userInfo'] === true) {
                        this.doGetUserInfo(callback)
                    } else {
                        const button = this.wx.createUserInfoButton(buttonStyle);
                        button.onTap((res) => {
                            if (res.errMsg.indexOf(':ok') > -1 && !!res.rawData) {
                                // 获取用户信息
                                this.doGetUserInfo(callback)
                                button.hide()
                            }
                        });
                    }
                },
                fail: (err) => {
                    let userInfoResult: Base = new Base();
                    userInfoResult.parse(BaseCode.ThirdSDKReturnErrorCode.jsonWithMsg(`err: ${JSON.stringify(err)} `))
                    callback(userInfoResult)
                }
            });
        } else {
            let userInfoResult: Base = new Base();
            userInfoResult.parse(BaseCode.UserNotLogin.jsonWithMsg(""))
            callback(userInfoResult)
        }
    }

    private get billingType() {
        if (this.platform == "android" || this.platform == "windows") {
            return BillingType.ANDROID;
        } else if (this.platform == 'ios') {
            return BillingType.IOS;
        } else {
            return BillingType.UNKNOWN;
        }
    }

    private normalizeWeChatBillingResponse(resp: any) {
        let res: any;
        // 成功的情况下不存在errCode字段
        if (resp.errCode === undefined) {
            res = {
                code: BillingCode.Success.Code,
                msg: `Success: ${resp.errMsg}`
            }
        } else {
            switch (resp.errCode) {
                case 0:
                    res = {
                        code: BillingCode.Success.Code,
                        msg: `with code: ${resp.errCode} error: ${resp.errMsg}`
                    }
                    break;
                case 1:
                case -2:
                    res = {
                        code: BillingCode.Cancel.Code,
                        msg: `with code: ${resp.errCode} error: ${resp.errMsg}`
                    }
                    break;
                default:
                    res = {
                        code: BillingCode.ThirdSDKReturnErrorCode.Code,
                        msg: `with code: ${resp.errCode} error: ${resp.errMsg}`
                    }
            }
        }

        return res;
    }

    private androidBillingProcess(params: any, callback: Action<Base>) {
        const self = this;
        let billingResult: Base = new Base();

        params["success"] = function (resp: any) {
            const res = self.normalizeWeChatBillingResponse(resp)
            billingResult.parse(res)
            callback(billingResult)
        }

        params["fail"] = function (resp: any) {
            const res = self.normalizeWeChatBillingResponse(resp)
            billingResult.parse(res)
            callback(billingResult)
        }

        self.wx.requestMidasPaymentGameItem(params)
    }

    private iosBillingProcess(callback: Action<Base>) {
        this.wx.openCustomerServiceConversation({
            showMessageCard: true,
            sendMessageImg: ZeusServer.getBillingQrcodeImage(Module.WECHAT),
            sessionFrom: "会话来源，透传后台，byte<1000",
            fail: (err: any) => {
                let billingResult: Base = new Base();
                billingResult.parse(BillingCode.ThirdSDKReturnErrorCode.jsonWithMsg(""))
                callback(billingResult)
            },
            complete: () => {
                let billingResult: Base = new Base();
                billingResult.parse(BillingCode.JsApiUnknownResult.jsonWithMsg(""))
                callback(billingResult)
            }
        });
    }

    private unsupportedBillingProcess(callback: Action<Base>) {
        this.wx.showModal({
            title: '支付提示',
            content: '很抱歉，功能正在紧急开发中，请您先用手机进行游戏充值',
            showCancel: false,
            confirmText: '确定',
            success(resp: any) {
                let billingResult: Base = new Base();
                billingResult.parse(BaseCode.ThirdSDKReturnErrorCode.jsonWithMsg("platform not support."))
                callback(billingResult)
            },
        });
    }

    //微信支付
    public billing(billingParams: BillingParams, callback: Action<Base>) {
        const self = this;
        let billingResult: Base = new Base();
        billingParams.platform = self.platform;
        billingParams.billingType = self.billingType;
        billingParams.billingEnv = ZeusServer.getBillingEnv(Module.WECHAT);
        ZeusServer.billing(this.wx, "wechat", billingParams,
            function (resp) {
                if (resp.statusCode == 200) {
                    if (self.billingType == BillingType.ANDROID) {
                        let data = {
                            signData: resp.data.result.signData,
                            paySig: resp.data.result.paySig,
                            signature: resp.data.result.signature,
                        }
                        self.androidBillingProcess(data, callback)
                    } else if (self.billingType == BillingType.IOS) {
                        self.iosBillingProcess(callback)
                    } else {
                        self.unsupportedBillingProcess(callback)
                    }
                } else {
                    let res = {
                        code: BillingCode.ThirdSDKReturnErrorCode.Code,
                        msg: `billing failed with code: ${resp.statusCode}, data: ${JSON.stringify(resp.data)}`
                    }
                    billingResult.parse(res)
                    callback(billingResult)
                }
            },
            function (resp: any) {
                let res = {
                    code: BillingCode.ThirdSDKReturnErrorCode.Code,
                    msg: `billing failed with code: ${resp.errno}`
                }
                billingResult.parse(res)
                callback(billingResult)
            })
    }

    public showRewardedAd(placement: string, callback: Action<Ad>) {
        this._adManager.showRewardedAd(placement, callback)
    }

    public showInterstitialAd(placement: string, callback: Action<Ad>) {
        this._adManager.showInterstitialAd(placement, callback)
    }

    public onNetworkStatusChange(callback: Action<Network>) {
        this.wx.onNetworkStatusChange((res: any) => {
            let result: Network = new Network();
            let resp = {
                code: BaseCode.Success.Code,
                network: res.networkType,
                msg: `network status changed, network: ${res.networkType}`
            }
            result.parse(resp)
            callback(result)
        })
    }

    public openCustomerService() {
        this.wx.openCustomerServiceConversation({
            success: (res: any) => {
                Logger.debug("wechat open customer service successfully", res)
            },
            fail: (err: any) => {
                Logger.debug("wechat open customerServiceConversation failed", err)
            }
        })
    }

    public openQuestionnaire(activityId: string, callback: Action<Questionnaire>): void {
        const appId = 'wxd947200f82267e58'

        const listener = (res: any) => {
            const referrerInfo = res.referrerInfo || {};
            const fromAppId = referrerInfo.appId;
            const extraData = referrerInfo.extraData || {};
            const scene = res.scene;
            
            let result = new Questionnaire();
            let jsonData: any;
            
            // 检查是否从问卷小程序返回
            if (fromAppId === appId && Math.abs(scene - 10037) < 0.2) {
                jsonData = BaseCode.Success.jsonWithMsg("Success");
            } else {
                jsonData = BaseCode.Cancel.jsonWithMsg(`back from appId: ${fromAppId}, scene: ${scene}`);
            }
            
            jsonData['result'] = extraData;
            jsonData['questionnaire_code'] = activityId;
            
            result.parse(jsonData);
            callback(result);
            
            this.wx.offShow(listener);
        };

        this.wx.onShow(listener);

        this.wx.navigateToMiniProgram({
            appId: appId,
            path: `pages/wjxqList/wjxqList?activityId=${activityId}&navigateBackMiniProgram=true`,
            success: (res: any) => {
                Logger.debug('navigate to questionnaire success', res);
            },
            fail: (err: any) => {
                Logger.error('navigate to questionnaire failed', err);
                let result = new Questionnaire();
                const jsonData = BaseCode.Cancel.jsonWithMsg(
                    `Navigate failed: ${err.errMsg}`
                );
                result.parse(jsonData);
                callback(result);
                this.wx.offShow(listener);
            }
        });


    }

    public checkUpdate() {
        const self = this;
        const updateManager = this.wx.getUpdateManager();
        updateManager.onCheckForUpdate(function(res) {
            // 请求完新版本信息的回调
            Logger.info("onCheckForUpdate:", res.hasUpdate);
        });
        updateManager.onUpdateReady(function () {
            self.wx.showModal({
                title: '更新提示',
                content: '新版本已经准备好，是否重启应用？',
                success: function (res: any) {
                    if (res.confirm) {
                        // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
                        updateManager.applyUpdate()
                    }
                }
            })
        });
        updateManager.onUpdateFailed(function() {
            // 新版本下载失败
            Logger.error('onUpdateFailed')
        });
    }

    public setRankData(option: object) {
        const kvData: { [x: string]: string; }[] = [];
        for (const key in option) {
            if (option.hasOwnProperty(key)) {
                kvData.push({
                    key: key,
                    value: String(option[key])
                });
            }
        }

        const opt = {
            KVDataList: kvData,
            complete: () => {
                Logger.info('setRankData complete')
            },
            success: (res) => {
                Logger.info('setRankData success', res)
            },
            fail: (err) => {
                Logger.error('setRankData fail', err)
            }
        };
        this.wx.setUserCloudStorage(opt);
    }

    public showRankList(dict: object) {
        const option = new RankOption(dict);
        if (option.rankType == 'group') {
            option.shareTicket = this._shareTicket
        }
        this.requirePrivacyAuthorize(
            () => {
                const msgData = {
                    ...option
                }

                this.getContext().postMessage(msgData);
            },
            ({ errMsg }) => {
                Logger.error("user reject to auth privacy " + errMsg);
            }
        );
    }

    private requirePrivacyAuthorize(success, fail) {
        const opt = {
            complete: (result) => {
                Logger.info(`complete: ${JSON.stringify(result)}`);
            },
            success: (result) => {
                Logger.info(`success: ${JSON.stringify(result)}`);
                success(result.errMsg);
            },
            fail: (result) => {
                Logger.error(`fail: ${JSON.stringify(result)}`);
                fail(result.errMsg);
            }
        };

        this.wx.requirePrivacyAuthorize(opt);
    }

    public hideOpenData() {
        this.requirePrivacyAuthorize(
            () => {
                const msgData = {
                    type: 'engine',
                    event: 'viewport',
                    rankType: 'hide',
                }
                this.getContext().postMessage(msgData);
            },
            ({ errMsg }) => {
                Logger.error("user reject to auth privacy " + errMsg);
            }
        );
    }

    private getContext() {
        if (!this._rankListOpenDataContext) {
            this._rankListOpenDataContext = this.wx.getOpenDataContext({
                sharedCanvasMode: 'screenCanvas'
            });
        }
        return this._rankListOpenDataContext;
    }

    public subscribeMessage(tmplIds: string[], callback: Action<Notification>) {
        this.wx.requestSubscribeMessage({
            tmplIds: tmplIds,
            success: (res) => {
                if (res.errMsg === 'requestSubscribeMessage:ok') {
                    let result: Notification = new Notification();
                    const jsonData = BaseCode.Success.jsonWithMsg("");
                    jsonData["topicResult"] = {};
                    // 复制除 errMsg 外的所有属性
                    for (const key in res) {
                        if (key !== 'errMsg') {
                            jsonData["topicResult"][key] = res[key];
                        }
                    }
                    result.parse(jsonData);
                    callback(result);
                }
            },
            fail: (err) => {
                let result: Notification = new Notification();
                const jsonData = BaseCode.ThirdSDKReturnErrorCode.jsonWithMsg("");
                jsonData["topicResult"] = {};
                // 对于失败情况，保持原有逻辑
                for (const key in err) {
                    if (key !== 'errMsg') {
                        jsonData["topicResult"][key] = err[key];
                    }
                }
                result.parse(jsonData);
                callback(result);
            }
        })
    }

    public sendSubscribeMessage(openId: string, tmplId: string, callback: Action<Base>) {
        ZeusServer.sendSubscribeMessage(this.wx, "wechat", openId, tmplId,
            function (resp) {
                let result: Base = new Base();
                const jsonData = BaseCode.ThirdSDKReturnErrorCode.jsonWithMsg("");
                result.parse(jsonData)
                callback(result)
            },
            function (resp) {
                let result: Base = new Base();
                const jsonData = BaseCode.ZeusServiceFail.jsonWithMsg("");
                result.parse(jsonData)
                callback(result)
            })
    }

    public share(options: ShareOptions, callback: Action<Base>) {
        this.wx.updateShareMenu({
            withShareTicket: true,
            success: (res) => {
                Logger.info('updateShareMenu res', res)
            }
        })
        this._shareStartTime = Date.now();
        this.wx.shareAppMessage({
            title: options.title,
            imageUrl: options.imageUrl,
            query: options.query,
        });
        const listener = function (shareStartTime: any) {
            const endTime = Date.now();
            const duration = (endTime - shareStartTime) / 1000;
            if (duration > 3) {
                let result: Base = new Base();
                const jsonData = BaseCode.Success.jsonWithMsg("");
                result.parse(jsonData)
                callback(result)
            } else {
                let result: Base = new Base();
                const jsonData = BaseCode.Cancel.jsonWithMsg("");
                result.parse(jsonData)
                callback(result)
            }
        }
        if (!this.hasAddedOnShowListener) {
            this.wx.onShow((res) => {
                listener(this._shareStartTime);
                this._shareTicket = res.shareTicket;
            });
            this.hasAddedOnShowListener = true;
        }
    }

    public nativeToScene(): void {
        this.wx.showModal({
            title: '提示',
            content: '很抱歉，此功能暂不支持',
            showCancel: false,
            confirmText: '确定',
            success(resp: any) {},
        });
    };
}
