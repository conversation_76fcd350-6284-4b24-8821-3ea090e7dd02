import {Device} from "../../Helper/DeviceDetails";
import {WeChatAdapter} from "../../Modules/Platform/WeChat";
import {ByteDanceAdapter} from "../../Modules/Platform/ByteDance";
import {AndroidAdapter} from "../../Modules/Platform/Native";

export class PlatformFactory {
    public getInstance() {
        if (Device.isWeChat()) {
            return WeChatAdapter.getInstance();
        } else if (Device.isByteDance()) {
            return ByteDanceAdapter.getInstance();
        } else if (Device.isNative()) {
            return AndroidAdapter.getInstance()
        } else {
            throw new Error("platform not support")
        }
    }
}