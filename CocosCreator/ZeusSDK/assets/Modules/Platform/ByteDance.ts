import ZeusServer from "../../Helper/ZeusServer";
import {<PERSON>ingParams, LoginParams} from "../../Modules/Platform/Typing";
import Login, {LoginCode} from "../../Core/Runtime/Result/Login";
import {Action} from "../../Core/Runtime/Action";
import AccountConstants, {BIEvent, LoginType} from "../../Common/Constants";
import Base, {BaseCode} from "../../Core/Runtime/Result/Base";
import {BillingCode} from "../../Core/Runtime/Result/Billing";
import Ad from "../../Core/Runtime/Result/Advertiser";
import Network from "../../Core/Runtime/Result/Network";
import Questionnaire from "../../Core/Runtime/Result/Questionnaire";
import Logger from "../../Common/Logger";
import {Analytics} from "../../Modules/Analytics";
import AdManager from "./Ad/AdManager";
import {getAdManager} from "./Ad/AdManagerFactory";
import {LocalData} from "../../Helper/LocalData";
import {GetUserInfo} from "../../Core/Runtime/Result/GetUserInfo";
import {AdMedia, Module} from "../../Common/ZeusConfig";
import {ZeusSDKVersion} from "../../Helper/ZeusSDKVersion";
import {GravityEngineAgent} from "../../Modules/MMP/GravityEngineAgent";
import {GlobalContainer} from "../../Helper/GlobalData";
import Notification from "../../Core/Runtime/Result/Notification";
import {ZeusTracker} from "../../Core/Runtime/Event/zeusTracker";
import {IDouYinAdapter} from "../../Interfaces/BaseInterface";
import {InstantBaseAdapter, ShareOptions} from "../../Modules/Platform/BasePlatform";
import {InitResult} from "../../Core/Runtime/Result/init";

enum BillingType {
    IOS = "jsapi",
    ANDROID = "virtual-payment",
    UNKNOWN = "unknown"
}

const globalContainer = GlobalContainer.getInstance();

export interface RankData {
    dataType: number
    value: string
    priority: number
    zoneId: string
    success: Function
    fail: Function
}

export interface ShowRankListData {
    dataType: number
    relationType: string
    rankType: string
    suffix: string
    rankTitle: string
    zoneId: string
    success: Function
    fail: Function
}

export class ByteDanceAdapter extends InstantBaseAdapter{
    private readonly tt: any;
    private readonly loginType = "12"
    private _deviceInfo: any
    private _adManager: AdManager;
    private _analytics: Analytics | null = null;
    private static instance: ByteDanceAdapter | null = null;
    private readonly source_name: string = "douyin"
    private _isLogin: boolean;
    private _isInit: boolean;

    private constructor() {
        super();
        this.tt = globalThis.tt;
        this._analytics = Analytics.getInstance();
        this._analytics.setSuperProperties({"source_name": this.source_name})
    }

    private get deviceInfo() {
        if (!this._deviceInfo) {
            if (this.tt.canIUse('getDeviceInfoSync')) {
                this._deviceInfo = this.tt.getDeviceInfoSync();
            } else if (this.tt.canIUse('getSystemInfoSync')) {
                this._deviceInfo = this.tt.getSystemInfoSync();
            }
        }

        return this._deviceInfo;
    }

    get platform() {
        return this.deviceInfo?.platform
    }

    public static getInstance() {
        if (!this.instance) {
            this.instance = new ByteDanceAdapter();
        }
        return this.instance;
    }

    public init(callback: Action<InitResult>) {
        ZeusServer.gameInfo(this.tt,
            (res) => {
                Logger.debug("get game info successfully: ", res)
                const respResult = res.data?.result;
                if (respResult) {
                    if (respResult['douyinAdConfig']) {
                        globalContainer.setDouYinADConfig({
                            rewardAdUnitId: respResult['douyinAdConfig']['rewarded_ad_id'],
                            interstitialAdUnitId: respResult['douyinAdConfig']['interstitial_ad_id']
                        })
                    }
                }
                respResult['code'] = BaseCode.Success.Code;
                this._isInit = true;
                this._adManager = getAdManager(false, this.tt, AdMedia.BYTE_DANCE)
                const tracker = ZeusTracker.getInstance();
                tracker.init();
                callback(respResult);
            },
            (err) => {
                Logger.debug("get game info failed: ", err)
                let baserResult = new InitResult();
                const jsonData = BaseCode.ZeusServiceFail.jsonWithMsg(err.data.message);
                baserResult.parse(jsonData);
                callback(baserResult);
            }
        )
    }

    public login(callback: Action<InitResult | Login>) {
        const self = this;
        if (!self._isInit) {
            let result = new InitResult();
            result.parse(BaseCode.SDKNotInit.jsonWithMsg(''));
            callback(result);
            return;
        }
        Logger.debug("check douyin session status")
        this.tt.checkSession({
            success: function (res: any) {
                let account = LocalData.get(AccountConstants.LOCAL_ACCOUNT_KEY)
                Logger.debug("trying to get account from local storage: ", account, "result: ", res)
                if (account) {
                    Logger.debug("found account in local storage: ", account, " start verify login status with Zeus Server");
                    let loginResult = new Login();
                    ZeusServer.verifyLoginStatus(self.tt, account,
                        function (res) {
                            Logger.debug("verify login status successfully: ", res);
                            if (res && res?.data?.result) {
                                res.data.result["login_type"] = LoginType.DouYin;
                                loginResult.parse(ZeusServer.normalizeZeusServerResponse(res));
                                self.loginSuccess(loginResult);

                                self._analytics.setSuperProperties({
                                    "open_id": loginResult.Detail.openid,
                                    "zeus_open_id": loginResult.Account,
                                    "zeus_login_way": loginResult.LoginType,
                                    "zeus_version": ZeusSDKVersion.SDKVersion,
                                })
                                self._analytics.login(account);
                                self._analytics.trackEvent(BIEvent.LOGIN_EVENT, {})

                                // 在静默登录场景下不会要求用户授权获取用户名称，这里用open id代替。
                                const props = {"user_name": loginResult.Detail.openid};
                                GravityEngineAgent.getInstance().setIdentifier(loginResult.Detail.openid, props)

                                callback(loginResult);
                            } else {
                                Logger.debug("verify login status failed: ", res);
                                self._isLogin = false;
                                loginResult.parse(ZeusServer.normalizeZeusServerResponse(res));
                                callback(loginResult);
                            }
                        },
                        function (res) {
                            Logger.debug("verify login status failed: ", res);
                            self._isLogin = false;
                            loginResult.parse(ZeusServer.normalizeZeusServerResponse(res));
                            callback(loginResult);
                        });
                } else {
                    Logger.debug("no account in local storage: ", account, " start login with douyin");
                    self.doLogin(callback)
                }
            },
            fail: function (res: any) {
                self._isLogin = false;
                Logger.debug("douyin session check failed: ", res, " start login with douyin")
                self.doLogin(callback)
            }
        })
    }

    private doLogin(callback: Action<Login>) {
        const self = this;
        let loginResult = new Login();
        this.tt.login({
            success: function (res: any) {
                const loginParams: LoginParams = {
                    loginType: self.loginType,
                    code: res.code,
                    platform: self.platform,
                    mobileInfo: self.deviceInfo
                }
                Logger.debug("douyin login successfully: ", res, " start login with Zeus Server");
                ZeusServer.login(self.tt, loginParams,
                    function (res) {
                        Logger.debug("Zeus Server login successfully: ", res)
                        res.data.result["login_type"] = LoginType.DouYin;
                        loginResult.parse(ZeusServer.normalizeZeusServerResponse(res));
                        self.loginSuccess(loginResult);

                        self._analytics.login(loginResult.Account);
                        self._analytics.setSuperProperties({
                            "open_id": loginResult.Detail.openid,
                            "zeus_open_id": loginResult.Account,
                            "zeus_login_way": loginResult.LoginType,
                            "zeus_version": ZeusSDKVersion.SDKVersion,
                        })
                        self._analytics.trackEvent(BIEvent.LOGIN_EVENT, {});

                        // 在静默登录场景下不会要求用户授权获取用户名称，这里用open id代替。
                        const props = {"user_name": loginResult.Detail.openid};
                        GravityEngineAgent.getInstance().setIdentifier(loginResult.Detail.openid, props);
                        if (loginResult.IsFirstRegister) {
                            GravityEngineAgent.getInstance().trackRegister();
                        }
                        callback(loginResult);
                    },
                    function (res) {
                        Logger.debug("Zeus Server login failed: ", res)
                        self._isLogin = false;
                        loginResult.parse(ZeusServer.normalizeZeusServerResponse(res));
                        callback(loginResult);
                    });
            },
            fail: function (err: any) {
                Logger.debug("douyin login failed: ", err)
                self._isLogin = false;
                const res = {
                    code: LoginCode.ThirdSDKReturnErrorCode.Code,
                    msg: `with code: ${err.errno} error: ${err.errMsg}`
                }
                loginResult.parse(res);
                callback(loginResult);
            },
            complete: function (res: any) {
                Logger.info('login complete', res);
            },
        })
    }

    private loginSuccess(loginResult: Login) {
        this._isLogin = true;
        const isSkipAd = loginResult.IsSkipAd;
        this._adManager = getAdManager(isSkipAd, this.tt, AdMedia.BYTE_DANCE)

        LocalData.save(AccountConstants.LOCAL_ACCOUNT_KEY, loginResult.Account);
        LocalData.save(AccountConstants.LOCAL_ID_KEY, loginResult.ID.toString());
    }

    private doGetUserInfo(callback: Action<Base>) {
        this.tt.getUserInfo({
            success: function (res: any) {
                Logger.info('getUserInfo success', res);
                let userInfoResult = new GetUserInfo();
                const result = {
                    code: BaseCode.Success.Code,
                    msg: res.errMsg,
                    errMsg: res.errMsg,
                    userInfo: res.userInfo
                };
                userInfoResult.parse(result);
                callback(userInfoResult);
            },
            fail: function(err) {
                Logger.error('getUserInfo fail', err);
                let userInfoResult = new GetUserInfo();
                const result = {
                    code: BaseCode.ThirdSDKReturnErrorCode.Code,
                    msg: err.errMsg,
                    errMsg: err.errMsg
                };
                userInfoResult.parse(result);
                callback(userInfoResult);
            },
        })
    }
    public getUserInfo(callback: Action<Base>) {
        if (this._isLogin) {
            this.tt.getSetting({
                success: (res) => {
                    if(res?.authSetting['scope.userInfo'] === true) {
                        this.doGetUserInfo(callback)
                    } else {
                        this.tt.authorize({
                            scope: 'scope.userInfo',
                            success: (data) => {
                                if(data?.data['scope.userInfo'] === 'ok') {
                                    this.doGetUserInfo(callback)
                                } else {
                                    let userInfoResult: Base = new Base();
                                    userInfoResult.parse(BaseCode.UnknownHostError.jsonWithMsg("authorize failed"))
                                    callback(userInfoResult)
                                }
                            },
                            fail: (err) => {
                                this.tt.openSetting({
                                    success: (res) => {
                                        Logger.info('openSetting success', res)
                                    },
                                    fail: (err) => {
                                        let userInfoResult: Base = new Base();
                                        userInfoResult.parse(BaseCode.ThirdSDKReturnErrorCode.jsonWithMsg(`err: ${JSON.stringify(err)} `))
                                        callback(userInfoResult)
                                    }
                                })
                            }
                        })
                    }
                },
                fail: (err)=> {
                    let userInfoResult: Base = new Base();
                    userInfoResult.parse(BaseCode.ThirdSDKReturnErrorCode.jsonWithMsg(`err: ${JSON.stringify(err)} `))
                    callback(userInfoResult)
                }
            })
        } else {
            let userInfoResult: Base = new Base();
            userInfoResult.parse(BaseCode.UserNotLogin.jsonWithMsg(""))
            callback(userInfoResult)
        }

    }

    private normalizeByteDanceBillingResponse(resp: any) {
        let res: any;
        // 成功的情况下不存在errCode字段
        if (resp.errCode === undefined) {
            res = {
                code: BillingCode.Success.Code,
                msg: `Success: ${resp.errMsg}`
            }
        } else {
            switch (resp.errCode) {
                case 0:
                    res = {
                        code: BillingCode.Success.Code,
                        msg: `with code: ${resp.errCode} error: ${resp.errMsg}`
                    }
                    break;
                case 1:
                case -2:
                    res = {
                        code: BillingCode.Cancel.Code,
                        msg: `with code: ${resp.errCode} error: ${resp.errMsg}`
                    }
                    break;
                default:
                    res = {
                        code: BillingCode.ThirdSDKReturnErrorCode.Code,
                        msg: `with code: ${resp.errCode} error: ${resp.errMsg}`
                    }
            }
        }

        return res;
    }

    private androidBillingProcess(params: BillingParams, resp: any, callback: Action<Base>) {
        // https://developer.open-douyin.com/docs/resource/zh-CN/mini-game/develop/api/payment/tt-request-game-payment
        const self = this;
        let billingResult: Base = new Base();

        let ttParam = {
            mode: "game",
            env: 0,
            zoneId: "1",
            orderAmount: params.price,
            currencyType: params.currency ?? "CNY", // ToDo iOS支持游戏币支付: DIAMOND
            goodName: params.productName,
            goodType: 2,
            platform: params.platform,
            customId: resp.data.result.order_id,
            extraInfo: params.extra,
            success: function (resp: any) {
                const res = self.normalizeByteDanceBillingResponse(resp)
                billingResult.parse(res)
                callback(billingResult)
            },
            fail: function (resp: any) {
                const res = self.normalizeByteDanceBillingResponse(resp)
                billingResult.parse(res)
                callback(billingResult)
            },
            complete: function (resp: any) {
                Logger.debug("requestGamePayment complete:", resp);
            }
        }

        self.tt.requestGamePayment(ttParam)
    }
    private iosBillingProcess(params: BillingParams, resp: any, callback: Action<Base>) {
        const self = this;
        let billingResult: Base = new Base();

        let ttParam = {
            zoneId: "1",
            orderAmount: params.price, // 单位为分，最小值为10
            currencyType: "DIAMOND",
            goodName: params.productName,
            goodType: 2, // 道具直购场景
            customId: resp.data.result.order_id,
            extraInfo: params.extra,
            success: function (resp: any) {
                const res = self.normalizeByteDanceBillingResponse(resp)
                billingResult.parse(res)
                callback(billingResult)
            },
            fail: function (resp: any) {
                const res = self.normalizeByteDanceBillingResponse(resp)
                billingResult.parse(res)
                callback(billingResult)
            },
            complete: function (resp: any) {
                Logger.debug("openAwemeCustomerService complete:", resp);
            }
        }

        self.tt.openAwemeCustomerService(ttParam)
    }

    private unsupportedBillingProcess(callback: Action<Base>) {
        this.tt.showModal({
            title: '支付提示',
            content: '很抱歉，功能正在紧急开发中，请您先用手机进行游戏充值',
            showCancel: false,
            confirmText: '确定',
            success(resp: any) {
                let billingResult: Base = new Base();
                billingResult.parse(BaseCode.ThirdSDKReturnErrorCode.jsonWithMsg("platform not support."))
                callback(billingResult)
            },
        });
    }

    public billing(billingParams: BillingParams, callback: Action<Base>) {
        const self = this;
        let billingResult: Base = new Base();
        billingParams.platform = self.platform;
        billingParams.billingType = BillingType.ANDROID;
        billingParams.billingEnv = ZeusServer.getBillingEnv(Module.BYTE_DANCE);
        if (billingParams.productName?.length > 10) {
            let res = {
                code: BillingCode.ParamError.Code,
                msg: `The length of the "productName" cannot exceed 10 characters`
            }
            billingResult.parse(res)
            callback(billingResult)
        } else {
            ZeusServer.billing(this.tt, "douyin", billingParams,
                function (resp) {
                    if (resp.statusCode == 200) {
                        if (self.platform == 'android') {
                            self.androidBillingProcess(billingParams, resp, callback)
                        } else if (self.platform == 'ios') {
                            self.iosBillingProcess(billingParams, resp, callback)
                        } else {
                            self.unsupportedBillingProcess(callback)
                        }
                    } else {
                        let res = {
                            code: BillingCode.ThirdSDKReturnErrorCode.Code,
                            msg: `billing failed with code: ${resp.statusCode}, data: ${resp.data}`
                        }
                        billingResult.parse(res)
                        callback(billingResult)
                    }
                },
                function (resp: any) {
                    let res = {
                        code: BillingCode.ThirdSDKReturnErrorCode.Code,
                        msg: `billing failed with code: ${resp.errno}`
                    }
                    billingResult.parse(res)
                    callback(billingResult)
                })
        }
    }

    public showRewardedAd(placement: string, callback: Action<Ad>) {
        this._adManager.showRewardedAd(placement, callback)
    }

    public showInterstitialAd(placement: string, callback: Action<Ad>) {
        this._adManager.showInterstitialAd(placement, callback)
    }

    public onNetworkStatusChange(callback: Action<Network>) {
        this.tt.onNetworkStatusChange((res: any) => {
            let result: Network = new Network();
            let resp = {
                code: BaseCode.Success.Code,
                network: res.networkType,
                msg: `network status changed, network: ${res.networkType}`
            }
            result.parse(resp)
            callback(result)
        })
    }

    public openCustomerService(callback: Action<Base>) {
        let options = {
            type: 1, // 小6客服
            success(){
                let result: Base = new Base();
                const res = {
                    code: BaseCode.Success.Code,
                    msg: 'openCustomerService success'
                }
                result.parse(res)
                callback(result)
            },
            fail(err){
                let result: Base = new Base();
                const res = {
                    code: BaseCode.ThirdSDKReturnErrorCode.Code,
                    msg: 'openCustomerService fail'
                }
                result.parse(res)
                callback(result)
            },
        }
        this.tt.openCustomerServiceConversation(options)
    }

    public openQuestionnaire(activityId: string, callback: Action<Questionnaire>): void {
        this.tt.showToast({
            title: "暂不可跳转",
            icon: "none",
        });
    }

    public checkUpdate() {
        const self = this;
        const updateManager = this.tt.getUpdateManager();
        updateManager.onCheckForUpdate((res: any) => {
            Logger.info("onCheckForUpdate:", res.hasUpdate);
        })
        updateManager.onUpdateReady(function () {
            self.tt.showModal({
                title: '更新提示',
                content: '新版本已经准备好，是否重启应用？',
                success: function (res: any) {
                    if (res.confirm) {
                        // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
                        updateManager.applyUpdate()
                    }
                }
            })
        })

        updateManager.onUpdateFailed((err: any) => {
            // 新的版本下载失败
            Logger.error("版本下载失败原因", err);
            self.tt.showToast({
                title: "新版本下载失败，请稍后再试",
                icon: "none",
            });
        });
    }

    public setRankData(rankData: object) {
        const data: RankData = {
            dataType: rankData['dataType'],
            value: rankData['value'],
            priority: rankData['priority'] || 0,
            zoneId: rankData['zoneId'] || 'default',
            success(res) {
                Logger.info('setImRankData success', res?.errMsg)
            },
            fail(err) {
                Logger.error('setImRankData fail:', err?.errMsg)
            }
        }

        this.tt.setImRankData(data)
    }


    public showRankList(option: object) {

        const params: ShowRankListData = {
            dataType: option['dataType'],
            relationType: option['relationType'],
            rankType: option['rankType'],
            suffix: undefined,
            rankTitle: undefined,
            zoneId: undefined,
            success(res) {
                Logger.info('showRankList success', res)
            },
            fail(err) {
                Logger.error('showRankList fail', err)
            }
        }
        if (option['suffix']) {
            params['suffix'] = option['suffix']
        }
        if (option['rankTitle']) {
            params['rankTitle'] = option['rankTitle']
        }
        if (option['zoneId']) {
            params['zoneId'] = option['zoneId']
        }

        this.tt.getImRankList(params)
    }

    public share(options: ShareOptions, callback: Action<Base>) {
        let result: Base = new Base();

        this.tt.shareAppMessage({
            ...options,
            success() {
                const jsonData = BaseCode.Success.jsonWithMsg("");
                result.parse(jsonData)
                callback(result)
            },
            fail(err) {
                // 检查是否是用户取消
                if (err?.errMsg?.includes('cancel') || err?.errNo === 10502) {
                    const res = {
                        code: BaseCode.Cancel.Code, // 使用取消状态码
                        msg: 'User cancelled sharing'
                    }
                    result.parse(res)
                } else {
                    const res = {
                        code: BaseCode.ThirdSDKReturnErrorCode.Code,
                        msg: err?.errMsg || 'shareAppMessage fail'
                    }
                    result.parse(res)
                }
                callback(result)
            }
        })
    }

    public nativeToScene() {
        this.tt.navigateToScene({
            scene: 'sidebar',
            success(res) {
                Logger.info('navigateToScene success', res)
            },
            fail(err) {
                Logger.error('navigateToScene fail', err)
            }
        })
    }

    public subscribeMessage(tmplIds: string[], callback: Action<Notification>) {
        this.tt.requestSubscribeMessage({
            tmplIds: tmplIds,
            success: (res) => {
                Logger.info('requestSubscribeMessage success', res)
                if (res.errMsg === 'requestSubscribeMessage:ok') {
                    let result: Notification = new Notification();
                    const jsonData = BaseCode.Success.jsonWithMsg("");
                    jsonData["topicResult"] = {};
                    // 复制除 errMsg 外的所有属性
                    for (const key in res) {
                        if (key !== 'errMsg') {
                            jsonData["topicResult"][key] = res[key];
                        }
                    }
                    result.parse(jsonData);
                    callback(result);
                }
            },
            fail: (err) =>  {
                Logger.error('requestSubscribeMessage fail', err)
                let result: Notification = new Notification();
                const jsonData = BaseCode.ThirdSDKReturnErrorCode.jsonWithMsg(err?.message);
                jsonData["topicResult"] = {};
                // 对于失败情况，保持原有逻辑
                for (const key in err) {
                    if (key !== 'errMsg') {
                        jsonData["topicResult"][key] = err[key];
                    }
                }
                result.parse(jsonData);
                callback(result);
            }
        })
    }

    public hideOpenData(): void {
        this.tt.showModal({
            title: '提示',
            content: '很抱歉，此功能暂不支持',
            showCancel: false,
            confirmText: '确定',
            success() {},
        });
    };
}
