import {native} from "cc"
import {NativeBaseAdapter, NativeShareOptions} from "./BasePlatform";
import {Action} from "../../Core/Runtime/Action";
import Ad from "../../Core/Runtime/Result/Advertiser";
import Base from "../../Core/Runtime/Result/Base";
import Network from "../../Core/Runtime/Result/Network";
import Questionnaire from "../../Core/Runtime/Result/Questionnaire";
import {BillingParams} from "./Typing";
import {GlobalContainer} from "../../Helper/GlobalData";
import Notification from "../../Core/Runtime/Result/Notification";
import {InitResult} from "../../Core/Runtime/Result/init";
import {DeviceIDResult} from "../../Core/Runtime/Result/deviceID";
import {GetUserInfo} from "../../Core/Runtime/Result/GetUserInfo";
import {IAndroidAdapter} from "../../Interfaces/BaseInterface";
import Login from "../../Core/Runtime/Result/Login";

const globalContainer = GlobalContainer.getInstance();

export class AndroidAdapter extends NativeBaseAdapter implements IAndroidAdapter{

    public platform: string;
    private static instance: AndroidAdapter | null;

    private constructor() {
        super();

    }

    public static getInstance() {
        if (!this.instance) {
            this.instance = new AndroidAdapter();
        }
        return this.instance;
    }

    protected registerNativeEventListener<T extends Base>(callbackId: string, action: Action<T>, ctor: new () => T): void {
        console.debug("[Script]: add listener");
        const listener = (arg: string) => {
            const json = JSON.parse(arg);
            const ins = new ctor();
            ins.parse(json ?? {})
            action(ins)
            console.debug("[Script]: callback complete " + JSON.stringify(ins));
            native.jsbBridgeWrapper.removeNativeEventListener(callbackId, listener)
        };

        native.jsbBridgeWrapper.addNativeEventListener(callbackId, listener);
    }

    protected dispatchEventToNative(event: string, arg?: string): any {
        console.debug("[Script]: dispatch invocation");
        native.jsbBridgeWrapper.dispatchEventToNative(event, arg);
    }

    public init(callback: Action<InitResult>): void {
        const appModel = globalContainer.getAppModel();
        this.registerNativeEventListener("onInit", callback, InitResult)
        this.dispatchEventToNative("init", JSON.stringify(appModel));
    }

    public login(callback: Action<Login>): void {
        this.registerNativeEventListener("onLogin", callback, Login)
        this.dispatchEventToNative("login");
    }

    public getUserInfo(callback: Action<GetUserInfo>): void {
        const callbackId = "getUserInfo";
        this.registerNativeEventListener("onGetUserInfo", callback, GetUserInfo)
        this.dispatchEventToNative(callbackId);
    }

    public billing(billingParams: BillingParams, callback: Action<Base>): void {
        const callbackId = "billing";
        this.registerNativeEventListener("onBilling", callback, Base)
        this.dispatchEventToNative(callbackId, JSON.stringify(billingParams));
    }

    public showRewardedAd(placement: string, callback: Action<Ad>): void {
        const callbackId = "showRewardedAd";
        this.registerNativeEventListener("onShowRewardedAd", callback, Ad)
        this.dispatchEventToNative(callbackId, placement);
    }

    public showInterstitialAd(placement: string, callback: Action<Ad>): void {
        const callbackId = "showInterstitialAd";
        this.registerNativeEventListener("onShowInterstitialAd", callback, Ad)
        this.dispatchEventToNative(callbackId, placement);
    }

    public onNetworkStatusChange(callback: Action<Network>): void {
        const callbackId = "networkStatusChange";
        this.registerNativeEventListener("onNetworkStatusChange", callback, Network)
        this.dispatchEventToNative(callbackId);
    }

    public openQuestionnaire(activityId: string, callback: Action<Questionnaire>): void {
        const callbackId = "questionnaire";
        this.registerNativeEventListener("onQuestionnaire", callback, Questionnaire)
        this.dispatchEventToNative(callbackId, activityId);
    }

    public setCustomerServiceUserInfo(kvs: Object, callback: Action<Base>): void {
        const callbackId = "setCustomerServiceUserInfo";
        this.registerNativeEventListener("onSetCustomerServiceUserInfo", callback, Base)
        this.dispatchEventToNative(callbackId, JSON.stringify(kvs));
    }

    public share(options: NativeShareOptions, callback: Action<Base>): void {
        const callbackId = "share";
        this.registerNativeEventListener("onShare", callback, Base)
        this.dispatchEventToNative(callbackId, JSON.stringify(options));
    }

    public getPushToken(callback: Action<Notification>): void {
        const callbackId = "getPushToken";
        this.registerNativeEventListener("onGetPushToken", callback, Notification)
        this.dispatchEventToNative(callbackId);
    }

    public adjustInit(appToken: string, isDebug: boolean): void {
        const callbackId = "adjustInit";
        const params = {
            appToken: appToken,
            isDebug: isDebug
        }

        this.registerNativeEventListener("onAdjustInit", null, Base)
        this.dispatchEventToNative(callbackId, JSON.stringify(params));
    }

    public track(eventName: string, eventToken: string, properties: object, callback: Action<Base>) : void {
        const callbackId = "track";
        const params = {
            eventName: eventName,
            eventToken: eventToken,
            properties: properties
        }

        this.registerNativeEventListener("onTrack", callback, Base)
        this.dispatchEventToNative(callbackId, JSON.stringify(params));
    }
    public trackRevenue(eventName: string, eventToken: string, price: number, currency: string, properties: object, callback: Action<Base>) : void {
        const callbackId = "trackRevenue";
        const params = {
            eventName: eventName,
            eventToken: eventToken,
            price: price,
            currency: currency,
            properties: properties
        }

        this.registerNativeEventListener("onTrackRevenue", callback, Base)
        this.dispatchEventToNative(callbackId, JSON.stringify(params));
    }

    public recover() {
        const callbackId = "recover";
        this.registerNativeEventListener("onRecover", null, Base)
        this.dispatchEventToNative(callbackId);
    }

    public getDeviceID(callback: Action<DeviceIDResult>) {
        const callbackId = "getDeviceID";
        this.registerNativeEventListener("onGetDeviceID", callback, DeviceIDResult)
        this.dispatchEventToNative(callbackId);
    }

    public faq() {
        const callbackId = "faq";
        this.registerNativeEventListener("onFaq", null, DeviceIDResult)
        this.dispatchEventToNative(callbackId);
    }
    public chat() {
        const callbackId = "chat";
        this.registerNativeEventListener("onChat", null, DeviceIDResult)
        this.dispatchEventToNative(callbackId);
    }

}