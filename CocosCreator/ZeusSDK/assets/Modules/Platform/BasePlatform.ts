import { Action } from "../../Core/Runtime/Action";
import Base from "../../Core/Runtime/Result/Base";
import { BillingParams } from "../../Modules/Platform/Typing";
import Ad from "../../Core/Runtime/Result/Advertiser";
import Network from "../../Core/Runtime/Result/Network";
import Questionnaire from "../../Core/Runtime/Result/Questionnaire";
import Notification from "../../Core/Runtime/Result/Notification";
import {InitResult} from "../../Core/Runtime/Result/init";
import {IBaseAdapter, IInstantAdapter, INativeAdapter} from "../../Interfaces/BaseInterface";
import {DeviceIDResult} from "../../Core/Runtime/Result/deviceID";

export interface ShareOptions {
    title?: string
    desc?: string
    extra?: object
    channel?: string
    query?: string
    templateId?: string
    imageUrl?: string
}
export interface NativeShareOptions {
    logoUrl?: string
    title?: string
    text?: string
    url?: string
    imageUrl?: string
    platform: string
}

export abstract class BaseAdapter implements IBaseAdapter {
    public abstract platform: string;
    public abstract init(callback: Action<InitResult>): void;
}

// Instant 基类（微信/抖音公共逻辑）
export abstract class InstantBaseAdapter extends BaseAdapter implements IInstantAdapter {
    public abstract platform: string;

    public abstract init(callback: Action<InitResult>): void;
    public abstract login(callback: Action<Base>): void;
    public abstract getUserInfo(callback: Action<Base>): void;
    public abstract billing(billingParams: BillingParams, callback: Action<Base>): void;
    public abstract showRewardedAd(placement: string, callback: Action<Ad>): void;
    public abstract showInterstitialAd(placement: string, callback: Action<Ad>): void;
    public abstract onNetworkStatusChange(callback: Action<Network>): void;
    public abstract openQuestionnaire(activityId: string, callback: Action<Questionnaire>): void;
    public abstract openCustomerService(callback: Action<Base>): void;
    public abstract share(options: ShareOptions, callback: Action<Base>): void;
    public abstract checkUpdate(): void;
    public abstract setRankData(rankData: object): void;
    public abstract hideOpenData(): void;
    public abstract showRankList(option: object): void;
    public abstract nativeToScene(): void;
    public abstract subscribeMessage(tmplIds: string[], callback: Action<Notification>): void;
}

export abstract class NativeBaseAdapter extends BaseAdapter implements INativeAdapter {
    public abstract platform: string;
    public abstract init(callback: Action<InitResult>): void;
    public abstract login(callback: Action<Base>): void;
    public abstract getUserInfo(callback: Action<Base>): void;
    public abstract billing(billingParams: BillingParams, callback: Action<Base>): void;
    public abstract showRewardedAd(placement: string, callback: Action<Ad>): void;
    public abstract showInterstitialAd(placement: string, callback: Action<Ad>): void;
    public abstract onNetworkStatusChange(callback: Action<Network>): void;
    public abstract openQuestionnaire(activityId: string, callback: Action<Questionnaire>): void;
    public abstract share(options: NativeShareOptions, callback: Action<Base>): void;
    public abstract getPushToken(callback: Action<Notification>): void;
    public abstract setCustomerServiceUserInfo(kvs: Object, callback: Action<Base>): void;
    public abstract track(eventName: string, eventToken: string, properties: object, callback: Action<Base>) : void;
    public abstract trackRevenue(eventName: string, eventToken: string, price: number, currency: string, properties: object, callback: Action<Base>) : void;
    public abstract recover(): void;
    public abstract getDeviceID(callback: Action<DeviceIDResult>): void;
}
