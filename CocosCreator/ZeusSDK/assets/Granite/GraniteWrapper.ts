import {Analytics} from "../Modules/Analytics";
import {GravityEngineAgent} from "../Modules/MMP/GravityEngineAgent";
import {PlatformFactory} from "../Modules/Platform/PlatformFactory"
import {BaseAdapter, InstantBaseAdapter, NativeBaseAdapter} from "../Modules/Platform/BasePlatform";
import {BaseCode} from "../Core/Runtime/Result/Base";
import {InitResult} from "../Core/Runtime/Result/init";
import {Action} from "../Core/Runtime/Action";
import {GlobalContainer, AppModel} from "../Helper/GlobalData";
import {ZeusSDKVersion} from "../Helper/ZeusSDKVersion";
import {IInstantAdapter, INativeAdapter} from "../Interfaces/BaseInterface";

export class GraniteWrapper {
    static AppPlatform: InstantBaseAdapter | NativeBaseAdapter;
    static Analytics: any;
    static Attribution: any;
    static Adjust: any;

    static init(appModel: AppModel, callback: Action<InitResult>) {
        const result = new InitResult();

        if (!appModel.AppId || !appModel.AppKey || !appModel.AppRequestURL) {
            result.parse(BaseCode.SDKNotInit.jsonWithMsg(`SDK initialization failed: Missing parameters AppId, AppKey or AppRequestURL.`));
            callback(result);
            return;
        }

        const globalContainer = GlobalContainer.getInstance();
        globalContainer.setAppModel(appModel);

        try {
            this.AppPlatform = new PlatformFactory().getInstance();
            // TODO
            // this.Analytics = Analytics.getInstance();
            // this.Attribution = GravityEngineAgent.getInstance();
        } catch (err) {
            result.parse(BaseCode.SDKNotInit.jsonWithMsg(`SDK initialization failed: ${err.message}`));
            callback(result);
            return;
        }

        this.AppPlatform.init((res) => {
            result.parse(res);
            callback(result)
        })
    }

    static getVersion() {
        return ZeusSDKVersion.SDKVersion;
    }

}