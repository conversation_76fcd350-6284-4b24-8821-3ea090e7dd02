package com.cocos.game;

import android.app.Activity;
import android.os.Bundle;

import com.cocos.lib.JsbBridgeWrapper;
import com.topjoy.zeussdk.bean.MCOrderInfoBean;
import com.topjoy.zeussdk.control.ZeusSDK;
import com.topjoy.zeussdk.control.ZeusSDKAnalytics;
import com.topjoy.zeussdk.bean.MCShareInfoBean;

import org.json.JSONException;
import org.json.JSONObject;
import android.util.Log;
import java.util.Map;
import java.util.Iterator;

public class ZeusCocosCreatorProxyApi {

    protected static Activity activity;

    private static void register(String functionName, JsbBridgeWrapper.OnScriptEventListener listener) {
        JsbBridgeWrapper.getInstance().addScriptEventListener(functionName, listener);
    }

    private static void dispatchToNative(String functionName, String params) {
        JsbBridgeWrapper.getInstance().dispatchEventToScript(functionName, params);
    }

    public static void hello(String msg) {
        System.out.println(msg);
    }

    public static void onCreate(Activity act, Bundle savedInstanceState) {
        activity = act;
        register("init", ZeusCocosCreatorProxyApi::init);
        register("login", ZeusCocosCreatorProxyApi::login);
        register("billing", ZeusCocosCreatorProxyApi::billing);
        register("recover", ZeusCocosCreatorProxyApi::recover);
        register("getPushToken", ZeusCocosCreatorProxyApi::getPushToken);
        register("share", ZeusCocosCreatorProxyApi::share);
        register("questionnaire", ZeusCocosCreatorProxyApi::questionnaire);
        register("setCustomerServiceUserInfo", ZeusCocosCreatorProxyApi::setCustomerServiceUserInfo);
        register("chat", ZeusCocosCreatorProxyApi::chat);
        register("faq", ZeusCocosCreatorProxyApi::faq);
        register("getUserInfo", ZeusCocosCreatorProxyApi::getUserInfo);
        register("showRewardedAd", ZeusCocosCreatorProxyApi::showRewardedAd);
        register("showInterstitialAd", ZeusCocosCreatorProxyApi::showInterstitialAd);
        register("adjustInit", ZeusCocosCreatorProxyApi::adjustInit);
        register("track", ZeusCocosCreatorProxyApi::track);
        register("trackRevenue", ZeusCocosCreatorProxyApi::trackRevenue);
        register("getDeviceID", ZeusCocosCreatorProxyApi::getDeviceID);
    }

    public static void onResume() {
        ZeusSDK.getInstance().onResume();
    }

    public static void onPause() {
        ZeusSDK.getInstance().onPause();
    }

    public static void init(String params) {
        JSONObject obj;
        try {
            obj = new JSONObject(params);
        } catch (JSONException ignored) {
            obj = new JSONObject();
        }
        String appId = obj.optString("AppId");
        String appKey = obj.optString("AppKey");
        String serverUrl = obj.optString("AppRequestURL");
        boolean isDebug = obj.optBoolean("DebugMode");
        activity.runOnUiThread(() -> ZeusSDK.getInstance().init(activity, appId, appKey, serverUrl, isDebug, (resultCode, msg, result) -> {
            dispatchToNative("onInit", result);
        }));
    }

    public static void login(String params) {
        activity.runOnUiThread(() -> ZeusSDK.getInstance().login((resultCode, msg, result) -> {
            dispatchToNative("onLogin", result);
        }));
    }

    public static void billing(String jsonParams) {
        Log.v("billing start", "billing start");
        MCOrderInfoBean orderInfo = (MCOrderInfoBean) parseBillingParams(jsonParams);

        // 新增：检查 orderInfo 是否为 null（解析失败时返回 null）
        if (orderInfo == null) {
            Log.e("billing", "订单信息解析失败，orderInfo 为 null");
            return;
        }

        Log.v("orderInfo", "productId: " + orderInfo.getProductID() + ", price: " + orderInfo.getPrice()); // 输出关键信息
        activity.runOnUiThread(() -> {
            // 新增：检查 activity 是否为 null（避免空指针）
            if (activity != null) {
                ZeusSDK.getInstance().pay(orderInfo, (resultCode, msg, result) -> {
                    Log.d("billing", "支付结果 - 状态码: " + resultCode + ", 信息: " + msg + ", 结果: " + result);
                    dispatchToNative("onBilling", result);
                });
            } else {
                Log.e("billing", "Activity 引用为 null，无法执行支付");
            }
        });
    }

    private static Object parseBillingParams(String jsonParams) {
        Log.v("parseBillingParams", "parseBillingParams start");
        try {
            JSONObject params = new JSONObject(jsonParams);
            MCOrderInfoBean orderInfo = new MCOrderInfoBean();

            // 处理必填字段 productId（使用 optString 避免异常，并校验非空）
            String productId = params.optString("productId", "");
            if (productId.isEmpty()) {
                Log.e("billing", "productId 为空，无法创建订单信息");
                return null;
            }
            orderInfo.setProductID(productId);

            // 其他字段使用 opt 方法处理缺失值
            orderInfo.setOldPurchaseID(params.optString("oldPurchaseId", ""));
            orderInfo.setPrice(params.optInt("price", 0)); // 提供默认值避免 NPE
            orderInfo.setExtendInfo(params.optString("extra", ""));
            orderInfo.setServerId(params.optString("serverId", ""));
            orderInfo.setServerName(params.optString("serverName", ""));
            orderInfo.setRoleId(params.optString("roleId", ""));
            orderInfo.setRoleName(params.optString("roleName", ""));
            orderInfo.setRoleVIP(params.optString("roleVIP", ""));
            orderInfo.setRoleLevel(params.optString("roleLevel", ""));
            orderInfo.setIsSubscription(params.optString("isSubscription", ""));
            orderInfo.setPayNotifyUrl(params.optString("billingCallbackUrl", ""));

            return orderInfo;
        } catch (JSONException e) {
            Log.e("billing", "JSON 解析失败: " + e.getMessage());
            e.printStackTrace();
            return null; // 解析失败时明确返回 null
        }
    }

    public static void recover(String params) {
        ZeusSDK.getInstance().recover();
    }

    public static void getPushToken(String params) {
        ZeusSDK.getInstance().getPushToken((resultCode, msg, result) -> {
            dispatchToNative("onGetPushToken", result);
        });
    }

    public static void share(String params) {
        Log.d("share", params);
        JSONObject obj = new JSONObject();
        try {
            obj = new JSONObject(params); // 解析JSON
        } catch (JSONException e) {
            Log.e("CustomerService", "无效的JSON: " + e.getMessage());
        }
        MCShareInfoBean shareInfo = new MCShareInfoBean() {
        };

        // 设置参数值
        shareInfo.setLogoUrl(obj.optString("logoUrl"));
        shareInfo.setTitle(obj.optString("title"));
        shareInfo.setText(obj.optString("text"));
        shareInfo.setUrl(obj.optString("url"));
        shareInfo.setImageUrl(obj.optString("imageUrl"));
        String platform = obj.optString("platform");

        activity.runOnUiThread(() -> ZeusSDK.getInstance().shareWithPlatform(activity, platform, shareInfo, (resultCode, msg, result) -> {
            dispatchToNative("onShare", result);
        }));
    }

    public static void questionnaire(String activityId) {
        activity.runOnUiThread(() -> ZeusSDK.getInstance().questionnaire(activityId, (resultCode, msg, result) -> {
            dispatchToNative("onQuestionnaire", result);
        }));
    }

    public static void setCustomerServiceUserInfo(String params) {
        JSONObject obj = new JSONObject();
        try {
            obj = new JSONObject(params); // 解析JSON
        } catch (JSONException e) {
            Log.e("CustomerService", "无效的JSON: " + e.getMessage());
        }

        // 构建键值对数组
        int size = obj.length() * 2;
        String[] kvs = new String[size];
        int index = 0;
        Iterator<String> keys = obj.keys();
        while (keys.hasNext()) {
            String key = keys.next();
            try {
                String value = obj.getString(key);
                kvs[index++] = key;
                kvs[index++] = value;
            } catch (JSONException e) {
                kvs[index++] = key;
                kvs[index++] = obj.opt(key) != null ? obj.opt(key).toString() : "";
            }
        }
        activity.runOnUiThread(() -> ZeusSDK.getInstance().setCustomerServiceUserInfo(kvs));
    }

    public static void chat(String params) {
        activity.runOnUiThread(() -> ZeusSDK.getInstance().showCustomerServiceChat());
    }

    public static void faq(String params) {
        activity.runOnUiThread(() -> ZeusSDK.getInstance().showCustomerServiceFAQs());
    }

    public static void getUserInfo(String params) {
        activity.runOnUiThread(() -> ZeusSDK.getInstance().facebookGetUserInfo((resultCode, msg, result) -> {
            dispatchToNative("onGetUserInfo", result);
        }));
    }

    public static void showRewardedAd(String placement) {
        activity.runOnUiThread(() -> ZeusSDK.getInstance().showApplovinMaxRewardAD(placement, (resultCode, msg, result) -> {
            dispatchToNative("onShowRewardedAd", result);
        }));
    }
    public static void showInterstitialAd(String placement) {
        activity.runOnUiThread(() -> ZeusSDK.getInstance().showApplovinMaxInterstitialAD((resultCode, msg, result) -> {
            dispatchToNative("onShowInterstitialAd", result);
        }));
    }

    public static void adjustInit(String params){
        JSONObject obj;
        try {
            obj = new JSONObject(params);
        } catch (JSONException ignored) {
            obj = new JSONObject();
        }

        String appToken = obj.optString("appToken");
        boolean isDebug = obj.optBoolean("isDebug");
        activity.runOnUiThread(() -> ZeusSDKAnalytics.getInstance().adjustSDKInit(appToken, isDebug));
    }

    public static void track(String params) {
        JSONObject obj;
        try {
            obj = new JSONObject(params);
        } catch (JSONException ignored) {
            obj = new JSONObject();
        }

        String eventToken = obj.optString("eventToken");
        if (eventToken.isEmpty()) {
            Log.e("track", "Adjust trackRevenue eventToken is empty");
            return;
        }
        if (obj.has("properties") && !obj.isNull("properties")) {
            JSONObject propertiesJson = obj.optJSONObject("properties");

            // 遍历properties的键值对
            Iterator<String> keys = propertiesJson.keys();
            while (keys.hasNext()) {
                String key = keys.next();
                Object value = propertiesJson.opt(key);

                // 将value转为字符串（处理不同类型值）
                String valueStr = value.toString();

                // 在UI线程调用方法（确保activity不为null）
                if (activity != null) {
                    activity.runOnUiThread(() ->
                            ZeusSDKAnalytics.getInstance().adjustSDKEventAddCallbackParameter(eventToken, key, valueStr)
                    );
                }
            }
        }
    }

    public static void trackRevenue(String params){
        JSONObject obj;
        try {
            obj = new JSONObject(params);
        } catch (JSONException ignored) {
            obj = new JSONObject();
        }
        String eventToken = obj.optString("eventToken");
        double price = obj.optDouble("price");
        String currency = obj.optString("currency");
        if (eventToken.isEmpty()) {
            Log.e("trackRevenue", "Adjust trackRevenue eventToken is empty");
            return;
        }

        if (obj.has("properties") && !obj.isNull("properties")) {
            JSONObject propertiesJson = obj.optJSONObject("properties");

            // 遍历properties的键值对
            assert propertiesJson != null;
            Iterator<String> keys = propertiesJson.keys();
            while (keys.hasNext()) {
                String key = keys.next();
                Object value = propertiesJson.opt(key);

                // 将value转为字符串（处理不同类型值）
                assert value != null;
                String valueStr = value.toString();

                // 在UI线程调用方法（确保activity不为null）
                if (activity != null) {
                    activity.runOnUiThread(() ->
                            ZeusSDKAnalytics.getInstance().adjustSDKEventAddCallbackParameter(eventToken, key, valueStr)
                    );
                }
            }
            activity.runOnUiThread(() ->
                    ZeusSDKAnalytics.getInstance().adjustSDKEventSetRevenue(eventToken, price, currency)
            );
        }
    }

    public static void getDeviceID(String params) {
        String deviceID = ZeusSDK.getInstance().getDeviceID(activity);
        // 构造回调结果（JSON 格式）
        JSONObject result = new JSONObject();
        try {
            result.put("deviceID", deviceID);
            result.put("code", 1);
            result.put("msg", "Success");
        } catch (JSONException e) {
            Log.e("getDeviceID", "JSON 构造失败: " + e.getMessage());
            result = new JSONObject();
        }

        JSONObject finalResult = result;
        activity.runOnUiThread(() ->
                dispatchToNative("onGetDeviceID", finalResult.toString())
        );
    }
}
