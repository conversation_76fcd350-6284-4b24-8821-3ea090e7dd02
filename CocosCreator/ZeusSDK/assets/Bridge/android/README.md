
## 如何集成

要集成ZeusSDK Cocos Creator Native版本首先需要项目通过Cocos Creator编译出Android版本。
然后按照如下的步骤完成项目配置：

1. 复制当前目录下的`ZeusCocosCreatorProxyApi.java`到目标项目的`native/engine/android/app/src/com/cocos/game`目录下。
2. 打开目标项目Project级别的`build.gradle`文件(Project: zeusCocosSDK)， 添加配置
```
allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://jitpack.io" }
        jcenter() // Warning: this repository is going to shut down soon
        maven { url "https://nexus.youle.game/repository/maven-topjoy/" }
    }
}
```
3. 打开目标项目Module级别的`build.gradle`文件(Module: zeusCocosSDK)， 在文件最顶端添加如下配置
```
buildscript {
    repositories {
        maven { url "https://nexus.youle.game/repository/maven-topjoy"}
        gradlePluginPortal()
        //添加gradle脚本远程库
        google()
        jcenter()
        mavenCentral()
        maven { url "https://jitpack.io" }

        // jcenter() // keeped as anchor, will be removed soon
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.0.2'
        classpath "com.android.tools.build:gradle:4.2.2"//最小版本4.1.0
        classpath 'com.google.gms:google-services:4.3.14'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.2'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
```
4. 在此Module级别的`build.gradle`文件(Module: zeusCocosSDK)的最外层级的`dependencies` 中添加如下implementation
```
dependencies {
    implementation('com.topjoy:zeussdk-android:3.0.18-GLOBAL')
}
外部项目对接由发行提供zeussdk aar包， 项目自行添加aar中缺少的依赖。
```
5. 如果工程编译时报错com.android.support和androidx存在duplicate的类时，在`gradle.properties`文件中添加配置
```
android.useAndroidX=true
android.enableJetifier=true
```
com.android.support已被官方废弃， 用androidx替代。
6. 修改`native/engine/android/app/src/com/cocos/game/AppActivity.java`文件中的代码
```
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // DO OTHER INITIALIZATION BELOW
        SDKWrapper.shared().init(this);
        ZeusCocosCreatorProxyApi.onCreate(this, savedInstanceState);
    }

    @Override
    protected void onResume() {
        super.onResume();
        SDKWrapper.shared().onResume();
        ZeusCocosCreatorProxyApi.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
        SDKWrapper.shared().onPause();
        ZeusCocosCreatorProxyApi.onPause();
    }
```
添加ZeusSDK生命周期函数调用.

## 参考资料

- cocos creator 官方桥接android示例: https://github.com/cocos/cocos-example-projects/tree/master/native-script-bridge