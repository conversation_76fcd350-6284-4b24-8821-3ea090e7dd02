import {Action} from "../Core/Runtime/Action";
import {InitResult} from "../Core/Runtime/Result/init";
import Base from "../Core/Runtime/Result/Base";
import {BillingParams} from "../Modules/Platform/Typing";
import Ad from "../Core/Runtime/Result/Advertiser";
import Network from "../Core/Runtime/Result/Network";
import Questionnaire from "../Core/Runtime/Result/Questionnaire";
import Notification from "../Core/Runtime/Result/Notification";
import {ShareOptions, NativeShareOptions} from "../Modules/Platform/BasePlatform";
import {DeviceIDResult} from "../Core/Runtime/Result/deviceID";

export interface IBaseAdapter {
    platform: String; // 获取当前平台类型
    init(callback: Action<InitResult>): void;
}

export interface IInstantAdapter extends IBaseAdapter {
    login(callback: Action<Base>): void;
    getUserInfo(callback: Action<Base>): void;
    billing(billingParams: BillingParams, callback: Action<Base>): void;
    showRewardedAd(placement: string, callback: Action<Ad>): void;
    showInterstitialAd(placement: string, callback: Action<Ad>): void;
    onNetworkStatusChange(callback: Action<Network>): void;
    openQuestionnaire(activityId: string, callback: Action<Questionnaire>): void;
    openCustomerService(callback: Action<Base>): void;
    share(options: ShareOptions, callback: Action<Base>): void;
    checkUpdate(): void;
    setRankData(rankData: object): void;
    hideOpenData(): void;
    showRankList(option: object): void;
    nativeToScene(): void;
    subscribeMessage(tmplIds: string[], callback: Action<Notification>): void;
}

// Native 公共接口
export interface INativeAdapter extends IBaseAdapter {
    login(callback: Action<Base>): void;
    getUserInfo(callback: Action<Base>): void;
    billing(billingParams: BillingParams, callback: Action<Base>): void;
    showRewardedAd(placement: string, callback: Action<Ad>): void;
    showInterstitialAd(placement: string, callback: Action<Ad>): void;
    onNetworkStatusChange(callback: Action<Network>): void;
    openQuestionnaire(activityId: string, callback: Action<Questionnaire>): void;
    share(options: NativeShareOptions, callback: Action<Base>): void;
    getPushToken(callback: Action<Notification>): void;
    setCustomerServiceUserInfo(kvs: Object, callback: Action<Base>): void;
    track(eventName: string, eventToken: string, properties: object, callback: Action<Base>) : void;
    trackRevenue(eventName: string, eventToken: string, price: number, currency: string, properties: object, callback: Action<Base>) : void;
    recover(): void;
    getDeviceID(callback: Action<DeviceIDResult>): void;
}

export interface IDouYinAdapter extends IInstantAdapter {}

export interface IWeChatAdapter extends IInstantAdapter {}

export interface IAndroidAdapter extends INativeAdapter {}

export interface IIosAdapter extends INativeAdapter {}