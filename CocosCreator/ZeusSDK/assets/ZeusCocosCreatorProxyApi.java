package com.cocos.game;

import android.app.Activity;
import android.os.Bundle;

import com.cocos.lib.JsbBridgeWrapper;
import com.topjoy.zeussdk.control.ZeusSDK;

import org.json.JSONException;
import org.json.JSONObject;


public class ZeusCocosCreatorProxyApi {

    protected static Activity activity;

    private static void register(String functionName, JsbBridgeWrapper.OnScriptEventListener listener) {
        JsbBridgeWrapper.getInstance().addScriptEventListener(functionName, listener);
    }

    private static void dispatchToNative(String functionName, String params) {
        JsbBridgeWrapper.getInstance().dispatchEventToScript(functionName, params);
    }

    public static void hello(String msg) {
        System.out.println(msg);
    }

    public static void onCreate(Activity act, Bundle savedInstanceState) {
        activity = act;
        register("int", params -> {
            try {
                init(params);
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
        });
        register("login", ZeusCocosCreatorProxyApi::login);
    }

    public static void onResume() {
        ZeusSDK.getInstance().onResume();
    }

    public static void onPause() {
        ZeusSDK.getInstance().onPause();
    }

    public static void init(String params) throws JSONException {
        JSONObject obj = new JSONObject(params);
        String appId = obj.getString("AppId");
        String appKey = obj.getString("AppKey");
        String serverUrl = obj.getString("AppRequestURL");
        boolean isDebug = obj.getBoolean("DebugMode");
        ZeusSDK.getInstance().init(activity, appId, appKey, serverUrl, isDebug, (resultCode, msg, result) -> {
            dispatchToNative("onInit", result);
        });
    }

    public static void login(String params) {
        ZeusSDK.getInstance().login((resultCode, msg, result) -> {
            dispatchToNative("onLogin", result);
        });
    }


}
