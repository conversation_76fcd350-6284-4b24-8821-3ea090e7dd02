// base64.ts

export default class Base64 {
    static _keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';

    static encode(organic: string) {
        let encoded = '';
        let chr1, chr2, chr3, enc1, enc2, enc3, enc4;
        let i = 0;
        const _keyStr = this._keyStr;
        organic = this._utf8_encode(organic);
        while (i < organic.length) {
            chr1 = organic.charCodeAt(i++);
            chr2 = organic.charCodeAt(i++);
            chr3 = organic.charCodeAt(i++);
            enc1 = chr1 >> 2;
            enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
            enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
            enc4 = chr3 & 63;
            if (isNaN(chr2)) {
                enc3 = enc4 = 64;
            } else if (isNaN(chr3)) {
                enc4 = 64;
            }
            encoded =
                encoded +
                _keyStr.charAt(enc1) +
                _keyStr.charAt(enc2) +
                _keyStr.charAt(enc3) +
                _keyStr.charAt(enc4);
        }
        return encoded;
    }

    static decode(organic: string) {
        let output = '';
        let chr1, chr2, chr3;
        let enc1, enc2, enc3, enc4;
        let i = 0;
        const _keyStr = this._keyStr;
        organic = organic.replace(/[^A-Za-z0-9\+\/\=]/g, '');
        while (i < organic.length) {
            enc1 = _keyStr.indexOf(organic.charAt(i++));
            enc2 = _keyStr.indexOf(organic.charAt(i++));
            enc3 = _keyStr.indexOf(organic.charAt(i++));
            enc4 = _keyStr.indexOf(organic.charAt(i++));
            chr1 = (enc1 << 2) | (enc2 >> 4);
            chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
            chr3 = ((enc3 & 3) << 6) | enc4;
            output = output + String.fromCharCode(chr1);
            if (enc3 != 64) {
                output = output + String.fromCharCode(chr2);
            }
            if (enc4 != 64) {
                output = output + String.fromCharCode(chr3);
            }
        }
        output = this._utf8_decode(output);
        return output;
    }

    static _utf8_encode(organic: string) {
        organic = organic.replace(/\r\n/g, '\n');
        let encoded = '';
        for (let n = 0; n < organic.length; n++) {
            const c = organic.charCodeAt(n);
            if (c < 128) {
                encoded += String.fromCharCode(c);
            } else if (c > 127 && c < 2048) {
                encoded += String.fromCharCode((c >> 6) | 192);
                encoded += String.fromCharCode((c & 63) | 128);
            } else {
                encoded += String.fromCharCode((c >> 12) | 224);
                encoded += String.fromCharCode(((c >> 6) & 63) | 128);
                encoded += String.fromCharCode((c & 63) | 128);
            }
        }
        return encoded;
    }

    static _utf8_decode(encoded: string) {
        let string = '';
        let i = 0;
        let c = 0,
            c2 = 0,
            c3 = 0;
        while (i < encoded.length) {
            c = encoded.charCodeAt(i);
            if (c < 128) {
                string += String.fromCharCode(c);
                i++;
            } else if (c > 191 && c < 224) {
                c2 = encoded.charCodeAt(i + 1);
                string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
                i += 2;
            } else {
                c2 = encoded.charCodeAt(i + 1);
                c3 = encoded.charCodeAt(i + 2);
                string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
                i += 3;
            }
        }
        return string;
    }
}
