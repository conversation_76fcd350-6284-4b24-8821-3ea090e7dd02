import {Md5} from "../Helper/HashLib";
import Base64 from "../Helper/Base64";
import {Device} from "../Helper/DeviceDetails";
import {getModuleConfigValue} from "../Common/ZeusConfig";
import {BillingParams, LoginParams} from "../Modules/Platform/Typing";
import Logger from "../Common/Logger";
import {BaseCode} from "../Core/Runtime/Result/Base";
import {LocalData} from "../Helper/LocalData";
import AccountConstants from "../Common/Constants";
import {GlobalContainer, AppModel} from "../Helper/GlobalData";


export default class ZeusServer {
    public static getFingerprint(params: Map<string, any>) {
        let keys = Object.keys(params)
        keys.sort()
        let sign = ''
        for (let key of keys) {
            let value = params[key]
            if (typeof value == "object") {
                sign += (key + JSON.stringify(value))
            } else {
                sign += (key + value.toString())
            }
        }

        const salt = this.getAppModel().AppKey.toString()
        sign += salt

        return Md5.hashStr(sign).toString()
    }

    public static getBillingQrcodeImage(module_name: string) {
        return getModuleConfigValue(module_name, "billing_qrcode_image_url").toString()
    }

    public static getBillingEnv(module_name: string) {
        return getModuleConfigValue(module_name, "billing_env").toString()
    }
    
    private static getAppModel(): AppModel {
        const globalContainer = GlobalContainer.getInstance();
        return globalContainer.getAppModel();
    }

    public static sendRequest(sender, params: any, sub_path: string, success: Function, fail: Function) {
        {
            let serverUrl = ZeusServer.getAppModel().AppRequestURL + sub_path;
            params['sign'] = ZeusServer.getFingerprint(params);

            let paramStr = JSON.stringify(params)
            let base64Str = Base64.encode(paramStr)

            sender.request({
                url: serverUrl,
                data: base64Str,
                header: {
                    "content-type": "application/x-www-form-urlencoded"
                },
                method: 'POST',
                success: function (res: any) {
                    Logger.info("request to backend server successfully:", res)
                    success(res)
                },
                fail: function (res: any) {
                    Logger.info("request to backend server failed:", res)
                    fail(res)
                }
            })
        }
    }

    public static normalizeZeusServerResponse(resp: any) {
        if (resp.statusCode != 200) {
            return {
                code: BaseCode.ZeusSDKFail.Code,
                msg: `${resp.errMsg}`
            }
        }

        if (resp.data.error_no != 0) {
            return {
                code: BaseCode.ZeusSDKFail.Code,
                msg: `${resp.data.message}`
            }
        }

        return {
            code: BaseCode.Success.Code,
            msg: `${resp.data.message}`,
            data: resp.data.result
        }
    }

    public static verifyLoginStatus(sender: any, account: string, success: Function, fail: Function) {
        const params = {
            appid: ZeusServer.getAppModel().AppId,
            device: Device.deviceId,
            account: account,
        }
        const subPath = "/minigame/user/login";

        ZeusServer.sendRequest(sender, params, subPath, success, fail);
    }

    public static login(sender: any, params: LoginParams, success: Function, fail: Function) {
        const _params = {
            appid: ZeusServer.getAppModel().AppId,
            device: Device.deviceId,
            type: params.loginType,
            code: params.code,
            platform: params.platform,
            mobile_info: JSON.stringify(params.mobileInfo)
        }
        const subPath = "/minigame/user/third-login";

        ZeusServer.sendRequest(sender, _params, subPath, success, fail);
    }

    public static billing(sender: any, sendName: string, params: BillingParams, success: Function, fail: Function) {
        let _params = {
            appid: ZeusServer.getAppModel().AppId,
            user_id: LocalData.get(AccountConstants.LOCAL_ID_KEY),
            product_id: params.productId,
            role_id: params.roleId,
            role_name: params.roleName,
            price: params.price.toString(),
            pay_type: params.billingType,
            pay_notify_url: params.billingCallbackUrl,
            platform: params.platform,
            env: params.billingEnv.toString(),
        }

        let subPath = ""
        if (sendName == "wechat") {
            subPath = "/minigame/order/wechat-exchange";
        } else if (sendName == "douyin") {
            subPath = "/minigame/order/douyin-exchange";
        }

        ZeusServer.sendRequest(sender, _params, subPath, success, fail);
    }
    public static sendSubscribeMessage(sender: any, sendName: string, openId: string, tmplId: string, success: Function, fail: Function) {
        let _params = {
            appid: ZeusServer.getAppModel().AppId,
            touser: openId,
            template_id: tmplId,
            page: 'index',
            miniprogram_state: 'developer',
            lang: 'zh_CN',
            data: {
                thing04: {
                    value: 'test_role_wy'
                },
                thing02: {
                    value: '测试内容'
                }
            }
        }
        let subPath = `/minigame/${sendName}/message/subscribe/send`
        ZeusServer.sendRequest(sender, _params, subPath, success, fail);
    }

    public static gameInfo(sender: any, success: Function, fail: Function) {
        const _params = {
            appid: ZeusServer.getAppModel().AppId,
            lang: "1"
        }
        const subPath = "/game/info";
        ZeusServer.sendRequest(sender, _params, subPath, success, fail);
    }
}
