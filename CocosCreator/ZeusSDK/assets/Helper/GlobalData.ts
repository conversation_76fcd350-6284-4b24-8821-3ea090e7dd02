export interface WechatADConfig {
    rewardAdUnitId: string
    interstitialAdUnitId: string
}

export interface DouYinADConfig {
    rewardAdUnitId: string
    interstitialAdUnitId: string
}

export interface AppModel {
    AppId: string; // 应用ID
    AppKey: string; // 应用Key
    AppRequestURL: string; // 应用请求后端服务地址
    UserProtocolURL: string; // 用户协议地址
    PrivacyProtocolURL: string; // 隐私协议地址
    DebugMode: boolean;// 是否开启Debug
}


// 定义一个全局变量容器
export class GlobalContainer {
    private static instance: GlobalContainer;

    private WechatADConfigKey: string = 'WechatADConfig';
    private DouYinADConfigKey: string = 'DouYinADConfig';
    private AppModelKey: string = 'AppModel';
    // 确保构造函数是私有的，以实现单例模式
    private constructor() {}

    // 获取单例实例
    public static getInstance(): GlobalContainer {
        if (!GlobalContainer.instance) {
            GlobalContainer.instance = new GlobalContainer();
        }
        return GlobalContainer.instance;
    }

    public setWechatADConfig(config: WechatADConfig): void {
        this.set(this.WechatADConfigKey, config);
    }

    public getWechatADConfig(): WechatADConfig {
        return this.get(this.WechatADConfigKey);
    }

    public setDouYinADConfig(config: DouYinADConfig): void {
        this.set(this.DouYinADConfigKey, config);
    }

    public getDouYinADConfig(): DouYinADConfig {
        return this.get(this.DouYinADConfigKey);
    }

    public setAppModel(appModel: AppModel): void {
        this.set(this.AppModelKey, appModel);
    }

    public getAppModel(): AppModel {
        return this.get(this.AppModelKey);
    }

    // 存储全局变量
    private set(key: string, value: any): void {
        globalThis[key] = value;
    }

    // 获取全局变量
    private get(key: string): any {
        return globalThis[key];
    }

    // 删除全局变量
    private delete(key: string): void {
        delete globalThis[key];
    }
}

