import {sys} from "cc"

// copy from cc.private._pal_system_info_enum_type_platform__Platform
export enum RuntimePlatform {
    UNKNOWN = "UNKNOWN",
    EDITOR_PAGE = "EDITOR_PAGE",
    EDITOR_CORE = "EDITOR_CORE",
    MOBILE_BROWSER = "MO<PERSON><PERSON>_BROWSER",
    DESKTOP_BROWSER = "DESKTOP_BROWSER",
    WIN32 = "WIN32",
    ANDROID = "ANDROID",
    IOS = "IOS",
    MACOS = "MACOS",
    OHOS = "OHOS",
    OPENHARMONY = "OPENHARMONY",
    WECHAT_GAME = "WECHAT_GAME",
    WECHAT_MINI_PROGRAM = "WECHAT_MINI_PROGRAM",
    BAIDU_MINI_GAME = "BAIDU_MINI_GAME",
    XIAOMI_QUICK_GAME = "XIAOMI_QUICK_GAME",
    ALIPAY_MINI_GAME = "ALIPAY_MINI_GAME",
    TAOBAO_CREATIVE_APP = "TAOBAO_CREATIVE_APP",
    TAOBAO_MINI_GAME = "TAOBAO_MINI_GAME",
    BYTEDANCE_MINI_GAME = "BYTEDANCE_MINI_GAME",
    OPPO_MINI_GAME = "OPPO_MINI_GAME",
    VIVO_MINI_GAME = "VIVO_MINI_GAME",
    HUAWEI_QUICK_GAME = "HUAWEI_QUICK_GAME",
    COCOSPLAY = "COCOSPLAY",
    LINKSURE_MINI_GAME = "LINKSURE_MINI_GAME",
    QTT_MINI_GAME = "QTT_MINI_GAME"
}

class _Device {

    get runtimePlatform() {
        return sys.platform
    }

    get os() {
        return sys.os
    }

    get networkType() {
        return sys.getNetworkType()
    }

    get deviceId() {
        let device_id: string;
        if (this.isBrowser() || this.isMiniGame()) {
            const device_id_key = "pseudo_device_id"
            const stringEmpty = ""
            device_id = sys.localStorage.getItem(device_id_key) ?? stringEmpty;
            if (device_id == stringEmpty) {
                // 生成8位16进制数字构成的id
                for (let i = 0; i < 32; i++) {
                    device_id += Math.floor(Math.random() * 16).toString(16)
                }
                sys.localStorage.setItem(device_id_key, device_id)
            }
        } else {
            // ToDo 桥接Android， iOS原生Zeus实现。
            throw new Error("not supported yet")
        }

        return device_id
    }

    _isNative: boolean | null = null

    isNative() {
        if (this._isNative == null) {
            let platform: any = this.runtimePlatform
            this._isNative = (platform == RuntimePlatform.ANDROID
                || platform == RuntimePlatform.MACOS
                || platform == RuntimePlatform.IOS
                || platform == RuntimePlatform.WIN32)
        }
        return this._isNative
    }

    isMobile() {
        let platform: any = this.runtimePlatform
        return (platform == RuntimePlatform.ANDROID
            || platform == RuntimePlatform.IOS)
    }

    isWeChat() {
        let platform: any = this.runtimePlatform
        return platform == RuntimePlatform.WECHAT_GAME
    }

    isByteDance() {
        let platform: any = this.runtimePlatform
        return platform == RuntimePlatform.BYTEDANCE_MINI_GAME
    }

    isBrowser() {
        let platform: any = this.runtimePlatform
        return platform == RuntimePlatform.EDITOR_CORE
            || platform == RuntimePlatform.EDITOR_PAGE
            || platform == RuntimePlatform.DESKTOP_BROWSER
            || platform == RuntimePlatform.MOBILE_BROWSER
    }

    isMiniGame() {
        let platform: any = this.runtimePlatform
        return (platform == RuntimePlatform.WECHAT_GAME || platform == RuntimePlatform.BYTEDANCE_MINI_GAME)
    }

    isIOS(): boolean {
        return this.os == sys.OS.IOS;
    }

    isAndroid(): boolean {
        return this.os == sys.OS.ANDROID;
    }
}

export const Device = new _Device()
