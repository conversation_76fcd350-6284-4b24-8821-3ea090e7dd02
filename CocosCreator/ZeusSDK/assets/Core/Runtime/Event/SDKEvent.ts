import {ZeusTracker} from "./zeusTracker"
import {EventCode} from "./EventCode"

export class SDKEvent {
    public static readonly net_name: string = "net";
    public static readonly event_code: string = "event_code";               // 事件code
    public static readonly event_description: string = "event_description"; // 事件描述，网络
    public static readonly code: string = "code";                           // 状态码
    public static readonly msg: string = "msg";                             // 可读信息
    public static readonly result: string = "result";                       // 返回信息
    public static readonly native_code: string = "native_code";             // 原生SDK状态码
    public static readonly backend_code: string = "backend_code";           // 后端状态码
    public static readonly backend_message: string = "backend_message";     // 后端返回信息
    public static readonly net_time: string = "net_time";                   // 事件耗时
    public static readonly sdk_version: string = "sdk_version";             // sdk版本号
    public static readonly tag_version: string = "tag_version";             // tag版本号
    public static readonly user_id: string = "user_id";                     // 用户id
    public static readonly game_id: string = "game_id";                     // 产品项目代号；(如"dk、ncc、vega")
    public static readonly device_memory: string = "device_memory";         // 设备内存
    public static readonly base_url: string = "zeus_server_url";            // 请求ZEUS服务器根路径
    public static readonly trace_id: string = "trace_id";                   // 跟踪ID
    public static readonly zeus_device_id: string = "zeus_device_id";

    /** event fields */
    public eventName: string | null = null;
    public eventCode: string | null = null;
    public zeusMsg: string | null = null;
    public zeusCode: number = -1;
    public zeusResult: string | null = null;
    public nativeCode: string | null = null;
    public backendCode: string | null = null;
    public backendMessage: string | null = null;
    public description: string | null = null;
    public netTime: string | null = null;
    public traceID: string | null = null;

    public GetJson(): object {
        const jsonObject = {};
        if (this.eventCode) jsonObject[SDKEvent.event_code] = this.eventCode;
        if (this.zeusCode !== -1) jsonObject[SDKEvent.code] = this.zeusCode;
        if (this.zeusResult) jsonObject[SDKEvent.result] = this.zeusResult;
        if (this.nativeCode) jsonObject[SDKEvent.native_code] = this.nativeCode;
        if (this.backendCode) jsonObject[SDKEvent.backend_code] = this.backendCode;
        if (this.backendMessage) jsonObject[SDKEvent.backend_message] = this.backendMessage;
        if (this.description) jsonObject[SDKEvent.event_description] = this.description;
        if (this.netTime) jsonObject[SDKEvent.net_time] = this.netTime;
        if (this.traceID) jsonObject[SDKEvent.trace_id] = this.traceID;
        return jsonObject;
    }

    constructor(_event: EventCode, json: object) {
        if (!json) {
            return;
        }
        this.eventName = _event.description;
        this.eventCode = _event.code.toString();
        this.zeusMsg = json[SDKEvent.msg] ? json[SDKEvent.msg].toString() : null;
        this.zeusCode = json[SDKEvent.code] ? json[SDKEvent.code] : -1;
        this.zeusResult = json.toString();
        this.nativeCode = json[SDKEvent.native_code] ? json[SDKEvent.native_code].toString() : null;
        this.backendCode = json[SDKEvent.backend_code] ? json[SDKEvent.backend_code].toString() : null;
        this.backendMessage = json[SDKEvent.backend_message] ? json[SDKEvent.backend_message].toString() : null;
        this.description = json[SDKEvent.event_description] ? json[SDKEvent.event_description].toString() : null;
        this.netTime = json[SDKEvent.net_time] ? json[SDKEvent.net_time].toString() : null;
        this.traceID = json[SDKEvent.trace_id] ? json[SDKEvent.trace_id].toString() : null;
    }

    /**
     * 事件打点
     */
    public Track(): void {
        const tracker = ZeusTracker.getInstance();
        tracker.track(this);
    }
}
