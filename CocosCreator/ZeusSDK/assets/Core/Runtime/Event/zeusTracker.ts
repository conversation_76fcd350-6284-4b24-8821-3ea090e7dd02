import { SDKEvent } from "./SDKEvent";

export class ZeusTracker {
    private static instance: ZeusTracker | null = null;
    private THINKING_APPID = "226d1c95988343a78f35131292ef56af";
    private THINKING_URL = "https://oversea-log.topjoy.com";

    private constructor() {
        // 私有构造函数，防止外部创建实例
    }

    public static getInstance(): ZeusTracker {
        if (!ZeusTracker.instance) {
            ZeusTracker.instance = new ZeusTracker();
        }
        return ZeusTracker.instance;
    }

    public init() {
        TDAnalytics.init({
            serverUrl: this.THINKING_URL,
            appId: this.THINKING_APPID,
        });
    }

    public track(sdkEvent: SDKEvent) {
        TDAnalytics.track(sdkEvent.GetJson(), this.THINKING_APPID);
    }
}