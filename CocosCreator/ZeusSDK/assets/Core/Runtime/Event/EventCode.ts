export class EventCode {
    public static readonly INIT = new EventCode(1, "init");
    public static readonly LOGIN = new EventCode(2, "login");
    public static readonly RE_LOGIN = new EventCode(3, "relogin");
    public static readonly LOGIN_VIEW = new EventCode(4, "login_view");
    public static readonly LOGOUT = new EventCode(5, "logout");
    public static readonly DELETE_ACCOUNT = new EventCode(6, "delete_account");
    public static readonly GET_IN_APP_SKU_DETAIL = new EventCode(7, "get_in_app_sku_detail");
    public static readonly GET_SUBS_SKU_DETAIL = new EventCode(8, "get_subs_sku_detail");
    public static readonly GET_GOODS_LIST = new EventCode(9, "get_goods_list");
    public static readonly PAY = new EventCode(10, "pay");
    public static readonly ALIPAY = new EventCode(11, "alipay");
    public static readonly WXPAY = new EventCode(12, "wxpay");
    public static readonly SHARE = new EventCode(13, "share");
    public static readonly SHARE_WITH_PLATFORM = new EventCode(14, "share_with_platform");
    public static readonly UPLOAD_ROLE = new EventCode(15, "upload_role");
    public static readonly GET_BIND_LIST = new EventCode(16, "get_bind_list");
    public static readonly THIRD_LOGIN = new EventCode(17, "third_login");
    public static readonly BIND = new EventCode(18, "bind");
    public static readonly USER_CENTER = new EventCode(19, "user_center");
    public static readonly SDK_SHOW_DIALOG = new EventCode(20, "sdk_show_dialog");
    public static readonly EXIT_DIALOG = new EventCode(21, "exit_dialog");
    public static readonly SHOW_CUSTOMER_SERVICE_CHAT = new EventCode(22, "show_customer_service_chat");
    public static readonly SHOW_CUSTOMER_SERVICE_FAQS = new EventCode(23, "show_customer_service_faqs");
    public static readonly SET_CUSTOMER_SERVICE_USER_INFO = new EventCode(24, "set_customer_service_user_info");
    public static readonly QUESTIONNAIRE = new EventCode(25, "questionnaire");
    public static readonly OPEN_SUBSCRIPTION = new EventCode(26, "open_subscription");
    public static readonly TRANSLATION = new EventCode(27, "translation");
    public static readonly REVIEW = new EventCode(28, "review");
    public static readonly REVIEW_IN_APP = new EventCode(29, "review_in_app");
    public static readonly GET_UNREAD_MSG_FLAG = new EventCode(30, "get_unread_msg_flag");
    public static readonly GET_CURRENT_FCM_TOKEN = new EventCode(31, "get_current_fcm_token");
    public static readonly REAL_NAME = new EventCode(32, "real_name");
    public static readonly TIME_LIMIT = new EventCode(33, "time_limit");
    public static readonly PAY_LIMIT = new EventCode(34, "pay_limit");
    public static readonly GET_REMAINING_TIME = new EventCode(35, "get_remaining_time");
    public static readonly IS_GOOGLE_CONNECTED = new EventCode(36, "is_google_connected");
    public static readonly OPEN_MAX_DEBUGGER = new EventCode(37, "open_max_debugger");
    public static readonly PRELOAD_APPLOVIN_AD = new EventCode(38, "preload_applovin_ad");
    public static readonly SET_ADULT_FLAG = new EventCode(39, "set_adult_flag");
    public static readonly SET_CONSENT_FLAG = new EventCode(40, "set_consent_flag");
    public static readonly SET_DO_NOT_SELL_FLAG = new EventCode(41, "set_do_not_sell_flag");
    public static readonly SHOW_APPLOVIN_MAX_REWARD_AD = new EventCode(42, "show_applovin_max_reward_ad");
    public static readonly DEFER_APPLOVIN_INIT = new EventCode(43, "defer_applovin_init");
    public static readonly DO_APPLOVIN_INIT = new EventCode(44, "do_applovin_init");
    public static readonly RECOVER_ORDER = new EventCode(45, "recover_order");
    public static readonly IS_JAIL_BROKEN = new EventCode(46, "is_jail_broken");
    public static readonly PAY_CONSUME = new EventCode(47, "pay_consume");
    public static readonly FACEBOOK_INFO = new EventCode(48, "facebook_info");
    public static readonly WX_SHARE = new EventCode(49, "wx_share");
    public static readonly REGISTER = new EventCode(50, "register");

    public code: number;
    public description: string;

    constructor(code: number, description: string) {
        this.code = code;
        this.description = description;
    }
}