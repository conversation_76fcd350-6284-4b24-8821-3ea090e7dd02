import Base, {BaseCode} from "../../../Core/Runtime/Result/Base";
import StatusCodeUtils from "../../../Core/Runtime/Result/Utils";

export class InitResult extends Base {
    IsRechargeable: boolean | null;

    constructor() {
        super();
        this.IsRechargeable = null;
    }

    parse(resp: any) {
        super.parse(resp);

        // 重新设置 Status 后，需要同步更新 IsSucceeded
        this.Status = StatusCodeUtils.FromCode(
            resp.code !== undefined ? resp.code : BaseCode.Success.Code,
            BaseCode
        );
        this.IsSucceeded = this.Status.Code == BaseCode.Success.Code;

        // 解析 is_recharge_open 字段，支持数字和布尔值
        if (resp["is_recharge_open"] !== undefined && resp["is_recharge_open"] !== null) {
            const rechargeValue = resp["is_recharge_open"];
            // 处理数字类型：1 表示开启，0 表示关闭
            if (typeof rechargeValue === 'number') {
                this.IsRechargeable = rechargeValue === 1;
            }
            // 处理布尔类型
            else if (typeof rechargeValue === 'boolean') {
                this.IsRechargeable = rechargeValue;
            }
            // 处理字符串类型
            else if (typeof rechargeValue === 'string') {
                this.IsRechargeable = rechargeValue === '1' || rechargeValue.toLowerCase() === 'true';
            }
            else {
                this.IsRechargeable = null;
            }
        } else {
            this.IsRechargeable = null;
        }
    }
}