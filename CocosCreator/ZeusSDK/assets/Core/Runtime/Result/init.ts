import Base, {BaseCode} from "../../../Core/Runtime/Result/Base";
import StatusCodeUtils from "../../../Core/Runtime/Result/Utils";

export class InitResult extends Base {
    IsRechargeable: string | null;

    constructor() {
        super();
        this.IsRechargeable = null;
    }

    parse(resp: any) {
        super.parse(resp);
        this.Status = StatusCodeUtils.FromCode(
            resp.code !== undefined ? resp.code : BaseCode.Success.Code,
            BaseCode
        );

        this.IsRechargeable = resp["is_recharge_open"] !== undefined ? resp["is_recharge_open"].toString() : null;
    }
}