import Base, {BaseCode} from "../../../Core/Runtime/Result/Base";
import StatusCodeUtils from "../../../Core/Runtime/Result/Utils";

export class InitResult extends Base {
    IsRechargeable: string | null;

    constructor() {
        super();
        this.IsRechargeable = null;
    }

    parse(resp: any) {
        super.parse(resp);

        // 重新设置 Status 后，需要同步更新 IsSucceeded
        this.Status = StatusCodeUtils.FromCode(
            resp.code !== undefined ? resp.code : BaseCode.Success.Code,
            BaseCode
        );
        this.IsSucceeded = this.Status.Code == BaseCode.Success.Code;

        // 解析 is_recharge_open 字段，服务器返回数字类型，转换为字符串
        this.IsRechargeable = resp["is_recharge_open"] !== undefined ? resp["is_recharge_open"].toString() : null;
    }
}