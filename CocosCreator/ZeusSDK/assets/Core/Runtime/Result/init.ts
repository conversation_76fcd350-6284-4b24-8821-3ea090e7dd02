import Base from "../../../Core/Runtime/Result/Base";

export class InitResult extends Base {
    IsRechargeable: string | null;

    constructor() {
        super();
        this.IsRechargeable = null;
    }

    parse(resp: any) {
        // 调用父类的 parse 方法处理基础字段
        super.parse(resp);

        // 解析 is_recharge_open 字段，服务器返回数字类型，转换为字符串
        this.IsRechargeable = resp["is_recharge_open"] !== undefined ? resp["is_recharge_open"].toString() : null;
    }
}