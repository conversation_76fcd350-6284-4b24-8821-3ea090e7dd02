import Base, {BaseCode} from "../../../Core/Runtime/Result/Base";
import StatusCodeUtils from "../../../Core/Runtime/Result/Utils";

export class InitResult extends Base {
    IsRechargeable: boolean | null;

    constructor() {
        super();
        this.IsRechargeable = null;
    }

    parse(resp: any) {
        super.parse(resp);

        // 重新设置 Status 后，需要同步更新 IsSucceeded
        this.Status = StatusCodeUtils.FromCode(
            resp.code !== undefined ? resp.code : BaseCode.Success.Code,
            BaseCode
        );
        this.IsSucceeded = this.Status.Code == BaseCode.Success.Code;

        // 解析 is_recharge_open 字段，服务器返回数字类型：1 表示开启，0 表示关闭
        this.IsRechargeable = resp["is_recharge_open"] === 1;
    }
}