import Base, {BaseCode} from "./Base";
import StatusCodeUtils from "../../../Core/Runtime/Result/Utils";

export class DeviceIDResult extends Base {
    public DeviceID: string = "";

    constructor() {
        super();
    }

    public override parse(result: any) {
        super.parse(result);

        this.DeviceID = result["deviceID"] ?? "";
        this.Status = StatusCodeUtils.FromCode(
            result.code !== undefined ? result.code : BaseCode.Success.Code,
            BaseCode
        );
    }
}