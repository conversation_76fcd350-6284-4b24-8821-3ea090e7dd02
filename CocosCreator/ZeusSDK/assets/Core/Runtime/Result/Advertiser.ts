import Status from "../../../Core/Runtime/Result/Status";
import Base, {BaseCode} from "../../../Core/Runtime/Result/Base";
import StatusCodeUtils from "../../../Core/Runtime/Result/Utils";


export class AdCode extends BaseCode {
    public static ADDisplayed = new Status(7001, "AD displayed.");
    public static ADDisplayFailed = new Status(7002, "AD display failed.");
    public static ADClicked = new Status(7003, "AD clicked.");
    public static ADHidden = new Status(7004, "AD hidden.");
    public static ADLoaded = new Status(7005, "AD loaded.");
    public static ADIsNotReady = new Status(7006, "Ad is loading or loaded fail.");
    public static ADUserRewarded = new Status(7007, "User rewarded.");
    public static ADRevenuePaid = new Status(7008, "AD revenue paid.");
    public static ADSDKNotInit = new Status(7009, "AD SDK not initialized.");
}

export class ADResult {
    public adUnitId: string = null;
    public networkName: string = null;
    public networkPlacement: string = null;
    public placement: string = null;
    public requestLatencyMillis: number = 0;
    public creativeId: string = null;
    public adReviewCreativeId: string = null;
    public revenue: number = 0.0;
    public revenuePrecision: string = null;
    public dspName: string = null;
    public dspId: string = null;
}

export default class Ad extends Base {
    public ADResult: ADResult = new ADResult();
    public IsSucceeded: boolean = true;
    public Uuid: string = null;
    public Type: string = null;
    public Platform: string = null;

    public override parse(resp: any) {
        super.parse(resp);
        this.Status = StatusCodeUtils.FromCode(resp.code, AdCode);
        
        // 如果resp中包含msg，则更新Status.Message
        if (resp.msg) {
            this.Status.Message = resp.msg;
        }
        
        this.IsSucceeded = this.Status.Code === AdCode.Success.Code || 
                          this.Status.Code === AdCode.ADDisplayed.Code || 
                          this.Status.Code === AdCode.ADUserRewarded.Code || 
                          this.Status.Code === AdCode.ADRevenuePaid.Code ||
                          this.Status.Code === AdCode.ADClicked.Code ||
                          this.Status.Code === AdCode.ADHidden.Code;
        this.Uuid = resp.uuid;
        this.Type = resp.type;
        this.Platform = resp.platform;
    }
}