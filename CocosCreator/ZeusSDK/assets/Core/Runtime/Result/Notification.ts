import Base, {BaseCode} from "../../../Core/Runtime/Result/Base";
import StatusCodeUtils from "../../../Core/Runtime/Result/Utils";

export class TopicResult {
    [key: string]: string;
}

export default class Notification extends Base {
    public TopicResult: TopicResult = {};
    public IsSucceeded: boolean = false;

    public override parse(resp: any) {
        super.parse(resp);
        
        this.Status = StatusCodeUtils.FromCode(
            resp.code !== undefined ? resp.code : BaseCode.Success.Code, 
            BaseCode
        );
        
        if (resp.errMsg === "requestSubscribeMessage:ok") {
            // 复制所有属性，除了 errMsg
            const { errMsg, ...topicResults } = resp;
            this.TopicResult = topicResults;
        } else if (resp.topicResult) {
            this.TopicResult = resp.topicResult;
        }
    
        this.IsSucceeded = this.Status.Code === BaseCode.Success.Code;
    }
}