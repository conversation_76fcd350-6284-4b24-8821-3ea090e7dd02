import Status from "../../../Core/Runtime/Result/Status";
import Base, {BaseCode} from "../../../Core/Runtime/Result/Base";
import StatusCodeUtils from "../../../Core/Runtime/Result/Utils";
import {LoginType} from "../../../Common/Constants";

export class LoginCode extends BaseCode {
    public static readonly UserNotExist: Status = new Status(2001, "User not exist");
    public static readonly RegisterFail: Status = new Status(2002, "Register fail");
    public static readonly DeviceIsEmulator: Status = new Status(2003, "Device is emulator");
    public static readonly PassUnqualify: Status = new Status(2004, "Mobile phone number or Email is invalid");
    public static readonly PasswordUnqualify: Status = new Status(2005, "Password is invalid");
    public static readonly StatusFailByService: Status = new Status(2006, "service return login fail.");
    public static readonly PrefetchNumberFail: Status = new Status(2007, "Prefetch number failed.");
}


class LoginDetail {
    public openid: string;
    public session_key: string;
    public unionid: string;
}


export default class Login extends Base {
    public ID: number;
    public Account: string;
    public Token: string;
    public LoginType: string;
    public IsFirstRegister: boolean;
    public Detail: LoginDetail = new LoginDetail();
    public IsSkipAd: boolean;

    public override parse(resp: any) {
        super.parse(resp);
        this.Status = StatusCodeUtils.FromCode(resp.code, LoginCode);
        if (!resp.data) {
            return;
        }

        this.ID = resp.data.id;
        this.Account = resp.data.account;
        this.Token = resp.data.token;
        this.LoginType = LoginType[resp.data.login_type];
        this.IsFirstRegister = resp.data.first_register ?? false;
        this.Detail.openid = resp.data.detail?.openid;
        this.Detail.session_key = resp.data.detail?.session_key;
        this.Detail.unionid = resp.data.detail?.unionid;
        this.IsSkipAd = resp.data.skip_ad ?? false;
    }
}

// 成功：
// {
//     "error_no": 0,
//     "message": "成功",
//     "result": {
//         "id": 73832,
//         "account": *********,
//         "token": "iZLHBarVCjcBknYgeoVkvFljBJqfiULE",
//         "is_real_name_authentication": true,
//         "adult": false,
//         "trace_id": "593e0403868d4b8a997650dffe7139e4"
//         "detail": {
//             "openid": "_000B|soKQsnpla6rYlja3nMMly591FVJA4n"
//             "session_key": "TJsT+uZJoUDaf9u7mrURuQ==",
//             "unionid": "48a76159-473e-5ead-8948-712be28e62a3"
//         ｝
//     },
//     "trace_id": "593e0403868d4b8a997650dffe7139e4",
//     "code": 1,
//     "yk_login": true,
//     "reStartLogin": 0,
//     "status": 0,
//     "logintype": 0
// }
//
// 失败：
// {
//     "code": 4,
//     "msg": "com.topjoy.zeussdk.utils.MCNetUtil$OkHttpDns@5109c36 returned no addresses for zeus.youle.game",
//     "net_name": "login",
//     "status": 0,
//     "third": 0,
//     "type": 0
// }
