import Status from "../../../Core/Runtime/Result/Status";
import StatusCodeUtils from "../../../Core/Runtime/Result/Utils";

export class BaseCode {
    public static readonly Success: Status = new Status(1, "Success");
    public static readonly Cancel: Status = new Status(2, "User Cancelled");
    public static readonly NetworkError: Status = new Status(3, "Network Request Error");
    public static readonly UnknownHostError: Status = new Status(4, "No Address Associated With Host");
    public static readonly ParamError: Status = new Status(5, "Parameter Incorrect");
    public static readonly DataParseError: Status = new Status(6, "Data Parse Error");
    public static readonly SignError: Status = new Status(7, "Sign Error");
    public static readonly UserNotLogin: Status = new Status(8, "User Not Login Yet");
    public static readonly SDKNotInit: Status = new Status(9, "SDK NOT INIT");
    public static readonly ZeusServiceFail: Status = new Status(10, "Fail From Server");
    public static readonly ThirdSDKReturnErrorCode: Status = new Status(11, "Third SDK Returns Error");
    public static readonly ZeusSDKFail: Status = new Status(12, "Zeus SDK Fail");
    public static readonly AntiDupClick: Status = new Status(13, "Could not perform the action because it has been performed recently");
}

export default class Base {
    public Status: Status;
    public IsSucceeded: boolean;

    public parse(result: any) {
        this.Status = StatusCodeUtils.FromCode(result.code, BaseCode);
        const message: string = result["msg"] ?? "";
        if (message) {
            this.Status.Message = message;
        }
        this.IsSucceeded = this.Status.Code == BaseCode.Success.Code;
    }
}