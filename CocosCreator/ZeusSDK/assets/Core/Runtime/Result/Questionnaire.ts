import Base, {BaseCode} from "../../../Core/Runtime/Result/Base";
import StatusCodeUtils from "../../../Core/Runtime/Result/Utils";


export default class Questionnaire extends Base {
    public SurveyCode: string;
    public IsFinished: boolean;

    public override parse(resp: any) {
        super.parse(resp);
        this.Status = StatusCodeUtils.FromCode(resp.code, BaseCode);
        this.SurveyCode = resp.questionnaire_code ?? "";
        this.IsFinished = resp.code === BaseCode.Success.Code;
    }
}