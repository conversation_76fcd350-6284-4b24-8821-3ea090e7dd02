import Status from "../../../Core/Runtime/Result/Status";
import {BaseCode} from "../../../Core/Runtime/Result/Base";

export class BillingCode extends BaseCode {
    public static ProductTypeError = new Status(3001, "Product type error");
    public static BillingServiceDisconnected = new Status(3002, "Billing service disconnected");
    public static DataParseError2 = new Status(3003, "Data parse error");
    public static OrderInfoEmpty = new Status(3004, "Order info empty");
    public static CreateOrderFail = new Status(3005, "Create order fail");
    public static NotSupportSubscription = new Status(3006, "Not support subscription");
    public static BillingClientNotReady = new Status(3007, "BillingClient not ready");
    public static NotQuerySKUDetail = new Status(3008, "Not query sku detail");
    public static StartPayFail = new Status(3009, "Start pay fail");
    public static PayVerifyFail = new Status(3010, "Pay verify fail");
    public static PaySuccessConsumeFail = new Status(3011, "Pay success, consume fail");
    public static SystemNotSupport = new Status(3012, "System not support");
    public static JsApiUnknownResult = new Status(3013, "SDK cant know pay result, please handle it."); // 受限于 JsApi 支付无法提供准确的支付结果以及支付是否结束，需要项目组自行处理。
}