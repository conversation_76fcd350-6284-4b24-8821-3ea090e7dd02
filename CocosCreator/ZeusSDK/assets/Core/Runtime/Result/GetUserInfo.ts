import Base from "./Base";

class SDKUserInfo {
    public ID: string = "";
    public Name: string = "";
    public Email: string = ""; 
    public Picture: string = "";
    public Accounts: string[] = [];
}

export class GetUserInfo extends Base {
    public UserInfo: SDKUserInfo = new SDKUserInfo();
    public Friends: SDKUserInfo[] = [];

    constructor() {
        super();
    }

    public override parse(result: any) {
        // 首先解析基础状态信息
        super.parse(result);

        // 如果成功且有用户信息
        if (result.errMsg === "getUserInfo:ok" && result.userInfo) {
            this.parseUserInfo(this.UserInfo, {
                id: "",  // 微信getUserInfo不返回id
                name: result.userInfo.nickName,
                email: "",  // 微信getUserInfo不返回email
                avatar: result.userInfo.avatarUrl,
                accounts: []  // 微信getUserInfo不返回accounts
            });
        }

        if (this.IsSucceeded && result.data) {
            // 解析用户信息
            if (result.data.UserInfo) {
                this.parseUserInfo(this.UserInfo, result.data.UserInfo);
            }

            // 解析好友列表
            if (Array.isArray(result.data.Friends)) {
                this.Friends = result.data.Friends.map(friendData => {
                    const friend = new SDKUserInfo();
                    this.parseUserInfo(friend, friendData);
                    return friend;
                });
            }
        }

        if (result?.id && result?.name && result?.email) {
            this.parseUserInfo(this.UserInfo, result)
        }

        if(result?.friends?.data) {
            const friendsData = result.friends.data;
            if (Array.isArray(friendsData)) {
                this.Friends = friendsData.map(data => {
                    const friend = new SDKUserInfo();
                    this.parseUserInfo(friend, data);
                    return friend;
                });
            }
        }
    }

    private parseUserInfo(info: SDKUserInfo, data: any) {
        info.ID = data.id || "";
        info.Name = data.name || "";
        info.Email = data.email || "";
        info.Picture = "";
        if (data?.avatar) {
            info.Picture = data.avatar
        }
        if (data.picture?.data?.url) {
            info.Picture = data.picture?.data?.url;
        }

        info.Accounts = Array.isArray(data.accounts) ? data.accounts : [];
    }
}