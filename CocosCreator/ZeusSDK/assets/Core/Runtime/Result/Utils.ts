import Status from "../../../Core/Runtime/Result/Status";
import {BaseCode} from "../../../Core/Runtime/Result/Base";

export default class StatusCodeUtils {
    // 使用 Map 实现类型安全的缓存机制
    private static readonly statusMapping: Map<number, Status> = new Map();

    public static FromCode<T extends BaseCode>(code: number, type: T): Status {
        if (this.statusMapping.has(code)) {
            return this.statusMapping.get(code)!; // 非空断言运算符
        }

        for (const key of Object.keys(type) as (keyof T)[]) {
            const status = type[key];

            if (status instanceof Status && status.Code === code) {
                this.statusMapping.set(code, status);
                return status;
            }
        }

        return BaseCode.ZeusSDKFail;
    }
}
