//
//  TAContext.h
//  ThinkingSDK.default-Base-Core-Extension-Router-Util-iOS
//
//  Created by w<PERSON><PERSON> on 2022/10/7.
//


#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface TAContext : NSObject

@property(nonatomic, strong) id application;

@property(nonatomic, strong) NSDictionary *launchOptions;

@property(atomic, strong) NSDictionary *customParam;

+ (instancetype)shareInstance;

- (void)addServiceWithImplInstance:(id)implInstance serviceName:(NSString *)serviceName;

- (void)removeServiceWithServiceName:(NSString *)serviceName;

- (id)getServiceInstanceFromServiceName:(NSString *)serviceName;

@end

NS_ASSUME_NONNULL_END
