
#import <Foundation/Foundation.h>

#if __has_include(<ThinkingSDK/ThinkingAnalyticsSDK.h>)
#import <ThinkingSDK/ThinkingAnalyticsSDK.h>
#else
#import "ThinkingAnalyticsSDK.h"
#endif

#if __has_include(<ThinkingSDK/TDFirstEventModel.h>)
#import <ThinkingSDK/TDFirstEventModel.h>
#else
#import "TDFirstEventModel.h"
#endif

#if __has_include(<ThinkingSDK/TDEditableEventModel.h>)
#import <ThinkingSDK/TDEditableEventModel.h>
#else
#import "TDEditableEventModel.h"
#endif

#if __has_include(<ThinkingSDK/TDConfig.h>)
#import <ThinkingSDK/TDConfig.h>
#else
#import "TDConfig.h"
#endif

#if __has_include(<ThinkingSDK/TDPresetProperties.h>)
#import <ThinkingSDK/TDPresetProperties.h>
#else
#import "TDPresetProperties.h"
#endif

#if __has_include(<ThinkingSDK/TDDeviceInfo.h>)
#import <ThinkingSDK/TDDeviceInfo.h>
#else
#import "TDDeviceInfo.h"
#endif

#if __has_include(<ThinkingSDK/TDAnalytics+Public.h>)
#import <ThinkingSDK/TDAnalytics+Public.h>
#else
#import "TDAnalytics+Public.h"
#endif

#if __has_include(<ThinkingSDK/TDAnalytics+Multiple.h>)
#import <ThinkingSDK/TDAnalytics+Multiple.h>
#else
#import "TDAnalytics+Multiple.h"
#endif

#if __has_include(<ThinkingSDK/TDAnalytics+WebView.h>)
#import <ThinkingSDK/TDAnalytics+WebView.h>
#else
#import "TDAnalytics+WebView.h"
#endif

#if __has_include(<ThinkingSDK/TDAnalytics+ThirdParty.h>)
#import <ThinkingSDK/TDAnalytics+ThirdParty.h>
#else
#import "TDAnalytics+ThirdParty.h"
#endif

#if __has_include(<ThinkingSDK/ThinkingAnalyticsSDK+OldPublic.h>)
#import <ThinkingSDK/ThinkingAnalyticsSDK+OldPublic.h>
#else
#import "ThinkingAnalyticsSDK+OldPublic.h"
#endif
