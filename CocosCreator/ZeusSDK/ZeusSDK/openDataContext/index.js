const style = require('./render/style')
const template = require('./render/template')
const Layout = require('./engine').default;
  let __env = GameGlobal.wx || GameGlobal.tt || GameGlobal.swan;
  let sharedCanvas  = __env.getSharedCanvas();
  let sharedContext = sharedCanvas.getContext('2d');
  function draw(newTemplate) {
      Layout.clear();
      Layout.init(newTemplate, style);
      Layout.layout(sharedContext);
  }
  
  function clear() {
    Layout.clear();
  }
  
  function updateViewPort(data) { 
      Layout.updateViewPort({
          x: data.x,
          y: data.y,
          width: data.width,
          height: data.height,
      });
  }
  
// 处理不同rankType的数据获取逻辑
function fetchData(rankType, shareTicket, callback) {
  if (rankType === 'friend') {
      __env.getFriendCloudStorage({
          keyList: ['score'],
          success: (res) => {
            console.log('getFriendCloudStorage res', res);
            const rankData = {
              data: []
            };
            if (res && Array.isArray(res?.data)) {
              // 对数据进行排序
              const sortData = res.data.map(item => {
                const score = item.KVDataList.find(kv => kv.key === "score")?.value;
                return {
                  rankScore: score ? parseInt(score, 10) : 0,
                  avatarUrl: item.avatarUrl,
                  nickname: item.nickname
                };
              }).sort((a, b) => b.rankScore - a.rankScore);
             rankData['data'] = sortData;
              console.log('rankData', rankData);
            }
            
            // 使用新数据重新渲染模板
            const newTemplate = require('./render/template')(rankData);
            draw(newTemplate);
            if (callback) callback(rankData);
          },
          fail: (err) => {
              console.log('[getFriendCloudStorage] fail', err);
              if (callback) callback(null);
          }
      });
  } else if (rankType === 'group') {
      __env.getGroupCloudStorage({
          keyList: ['score'],
          shareTicket: shareTicket,
          success: (res) => {
            console.log('getGroupCloudStorage res', res)
            const rankData = {
              data: []
            };
            if (res && Array.isArray(res?.data)) {
              const sortData = res.data.map(item => {
                const score = item.KVDataList.find(kv => kv.key === "score")?.value;
                return {
                  rankScore: score ? parseInt(score, 10) : 0,
                  avatarUrl: item.avatarUrl || '',
                  nickname: item.nickname || ''
                };
              }).sort((a, b) => b.rankScore - a.rankScore);

              rankData['data'] = sortData
              console.log('new rankData', rankData)
            }
            // 使用新数据重新渲染模板
            const newTemplate = require('./render/template')(rankData);
            draw(newTemplate);
            if (callback) callback(rankData);
          },
          fail: (err) => {
              console.log('[getGroupCloudStorage] fail', err);
              if (callback) callback(null);
          }
      });
  }
}
  
  __env.onMessage(data => {
    if (data.type === 'engine' && data.event === 'viewport') {
        if (data.rankType === 'friend') {
            updateViewPort(data);
            fetchData('friend', data.shareTicket, () => {});
        } else if (data.rankType === 'group') {
            updateViewPort(data);
            fetchData('group', data.shareTicket, () => {});
        } else if (data.rankType === 'hide') {
            clear();
        }
    }
  });