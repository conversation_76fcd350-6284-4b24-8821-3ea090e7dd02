{"name": "@topjoy/zeus-cocos-sdk", "version": "1.0.0", "description": "Zeus SDK for Cocos Creator - A comprehensive SDK for analytics, platform integration, and mini-game development", "main": "index.js", "module": "index.js", "types": "index.d.ts", "type": "module", "files": ["index.js", "index.d.ts", "Common/**/*", "Core/**/*", "Granite/**/*", "Helper/**/*", "Modules/**/*", "Plugins/**/*", "Resources/**/*", "openDataContext/**/*"], "exports": {".": {"import": "./index.js", "types": "./index.d.ts"}, "./openDataContext": "./openDataContext/index.js", "./plugins/thinking": "./Plugins/ThinkingData/tdanalytics.mg.cocoscreator.min.js", "./plugins/gravity": "./Plugins/Gravity/gravityengine.mg.cocoscreator.min.js"}, "keywords": ["cocos-creator", "sdk", "analytics", "wechat", "bytedance", "mini-game", "thinking-data", "gravity"], "author": "TopJoy", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/topjoy/zeus-cocos-sdk.git"}, "bugs": {"url": "https://github.com/topjoy/zeus-cocos-sdk/issues"}, "homepage": "https://github.com/topjoy/zeus-cocos-sdk#readme", "engines": {"node": ">=14.0.0"}, "peerDependencies": {"cc": ">=3.8.0"}}